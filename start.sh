#!/bin/bash
# 海狸拾袋管理工具 - 启动脚本

echo "🚀 海狸拾袋管理工具启动脚本"
echo "================================"

# 检查Python版本
echo "📋 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装，请先安装Python 3.8+"
    exit 1
fi

# 检查虚拟环境
if [ -d ".venv" ]; then
    echo "🔧 激活虚拟环境..."
    source .venv/bin/activate
    if [ $? -eq 0 ]; then
        echo "✅ 虚拟环境已激活"
    else
        echo "❌ 虚拟环境激活失败"
        exit 1
    fi
else
    echo "⚠️  虚拟环境不存在，使用系统Python"
fi

# 运行验证脚本
echo "🔍 运行启动验证..."
python3 启动验证.py
if [ $? -ne 0 ]; then
    echo "❌ 启动验证失败，请检查配置"
    exit 1
fi

echo ""
echo "🎉 验证通过！正在启动应用..."
echo "================================"

# 启动Flask应用
python3 app_flask.py
