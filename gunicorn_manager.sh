#!/bin/bash
# Gunicorn管理脚本 - 海狸拾袋管理工具

# 配置变量
WORK_DIR="/opt/haili_web"
VENV_DIR="$WORK_DIR/.venv"
PID_FILE="$WORK_DIR/gunicorn.pid"
LOG_FILE="$WORK_DIR/logs/gunicorn.log"
CONFIG_FILE="$WORK_DIR/gunicorn.conf.py"
APP_MODULE="app_flask:app"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查工作目录
check_work_dir() {
    if [ ! -d "$WORK_DIR" ]; then
        print_error "工作目录不存在: $WORK_DIR"
        exit 1
    fi
    cd "$WORK_DIR"
}

# 检查虚拟环境
check_venv() {
    if [ ! -d "$VENV_DIR" ]; then
        print_error "虚拟环境不存在: $VENV_DIR"
        exit 1
    fi
}

# 检查配置文件
check_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        print_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
}

# 创建日志目录
ensure_log_dir() {
    local log_dir=$(dirname "$LOG_FILE")
    if [ ! -d "$log_dir" ]; then
        mkdir -p "$log_dir"
        print_info "创建日志目录: $log_dir"
    fi
}

# 获取进程PID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        echo ""
    fi
}

# 检查进程是否运行
is_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 启动服务
start_service() {
    print_info "启动Gunicorn服务..."
    
    # 检查是否已经运行
    if is_running; then
        local pid=$(get_pid)
        print_warning "Gunicorn已经在运行 (PID: $pid)"
        return 0
    fi
    
    # 检查环境
    check_work_dir
    check_venv
    check_config
    ensure_log_dir
    
    # 激活虚拟环境并启动
    source "$VENV_DIR/bin/activate"
    
    # 启动Gunicorn
    nohup gunicorn -c "$CONFIG_FILE" "$APP_MODULE" > "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    
    # 等待一下确认启动成功
    sleep 2
    
    if is_running; then
        print_success "Gunicorn启动成功 (PID: $pid)"
        print_info "日志文件: $LOG_FILE"
    else
        print_error "Gunicorn启动失败，请查看日志: $LOG_FILE"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 停止服务
stop_service() {
    print_info "停止Gunicorn服务..."
    
    local pid=$(get_pid)
    
    if [ -z "$pid" ]; then
        print_warning "PID文件不存在，Gunicorn可能未运行"
        return 0
    fi
    
    if ps -p "$pid" > /dev/null 2>&1; then
        print_info "正在停止进程 (PID: $pid)..."
        
        # 发送TERM信号
        kill -TERM "$pid"
        
        # 等待进程结束
        local count=0
        while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        # 如果还没结束，强制杀死
        if ps -p "$pid" > /dev/null 2>&1; then
            print_warning "进程未响应TERM信号，发送KILL信号..."
            kill -KILL "$pid"
            sleep 1
        fi
        
        if ps -p "$pid" > /dev/null 2>&1; then
            print_error "无法停止进程 (PID: $pid)"
            exit 1
        else
            print_success "Gunicorn已停止"
        fi
    else
        print_warning "进程不存在 (PID: $pid)"
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
}

# 重启服务
restart_service() {
    print_info "重启Gunicorn服务..."
    stop_service
    sleep 2
    start_service
}

# 查看状态
show_status() {
    local pid=$(get_pid)
    
    if [ -z "$pid" ]; then
        print_info "状态: 未运行"
        return 1
    fi
    
    if ps -p "$pid" > /dev/null 2>&1; then
        print_success "状态: 运行中 (PID: $pid)"
        
        # 显示进程信息
        echo ""
        echo "进程信息:"
        ps -p "$pid" -o pid,ppid,user,start,time,command
        
        # 显示端口信息
        echo ""
        echo "端口信息:"
        netstat -tlnp 2>/dev/null | grep ":$pid " || echo "未找到监听端口"
        
        return 0
    else
        print_error "PID文件存在但进程未运行 (PID: $pid)"
        print_info "清理PID文件..."
        rm -f "$PID_FILE"
        return 1
    fi
}

# 查看日志
show_logs() {
    if [ ! -f "$LOG_FILE" ]; then
        print_error "日志文件不存在: $LOG_FILE"
        exit 1
    fi
    
    print_info "显示日志文件: $LOG_FILE"
    print_info "按 Ctrl+C 退出日志查看"
    echo ""
    
    tail -f "$LOG_FILE"
}

# 显示帮助
show_help() {
    echo "Gunicorn管理脚本 - 海狸拾袋管理工具"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|help}"
    echo ""
    echo "命令:"
    echo "  start    启动Gunicorn服务"
    echo "  stop     停止Gunicorn服务"
    echo "  restart  重启Gunicorn服务"
    echo "  status   查看服务状态"
    echo "  logs     查看实时日志"
    echo "  help     显示此帮助信息"
    echo ""
    echo "配置:"
    echo "  工作目录: $WORK_DIR"
    echo "  虚拟环境: $VENV_DIR"
    echo "  配置文件: $CONFIG_FILE"
    echo "  日志文件: $LOG_FILE"
    echo "  PID文件:  $PID_FILE"
}

# 主函数
main() {
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
