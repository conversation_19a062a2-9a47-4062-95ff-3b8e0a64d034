"""
海狸拾袋管理工具 - 查询函数模块
此文件包含所有实际的查询函数，您可以在这里实现具体的业务逻辑
"""

import re
import datetime
import os
import requests, json, re
from urllib.parse import quote
import time
import sys
import hashlib
from threading import Lock

from config import TIME_SLEEP


def md5_encrypt(password):
    return hashlib.md5(password.encode("utf-8")).hexdigest()


def format_json_data(formatted_data):

    # 处理content字段
    if isinstance(formatted_data.get("content"), str):
        # 去除末尾的非JSON内容（从第一个换行符开始的所有内容）
        json_str = formatted_data["content"].split("\r\n")[0]

        try:
            # 将字符串解析为JSON对象
            formatted_data["content"] = json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            # 如果解析失败，保持原样
            formatted_data["content"] = json_str

    return json.dumps(formatted_data, indent=2, ensure_ascii=False)


def getGeneralUsableCouponListByPage(session, pin):
    """待使用券查询"""
    try:
        # 发送请求
        response = session.get(
            f"http://jsf.yibin.jdxpoc.com/iface/manage/invoke/362161?interfaceName=com.jd.mycoupon.client.service.MyCouponGeneralService&ip=***********&port=22000&safVer=210&token=&cmd=invoke+com.jd.mycoupon.client.service.MyCouponGeneralService.getGeneralUsableCouponListByPage(%7B%22userInfo%22%3A+%7B%22pin%22%3A%22{pin}%22%2C%22userLevel%22%3A0%2C%22totalScore%22%3A0%2C%22flagInfo+%22%3Anull%7D%2C%22couponTypeList%22%3Anull%2C%22couponStyleList%22%3Anull%2C%22sortColumnDict%22%3A%22BY_CREATE_TIME%22%2C%22sortTypeDict%22%3A%22BY_DESC%22%2C%22states%22%3Anull%2C%22ext%22%3Anull%7D%2C+%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%7D%2C%7B%22channel%22%3A1%2C%22systemName%22%3A%22yb-home%22%2C%22ip%22%3A%22125.69.161.71%22%2C%22country%22%3A%22country%22%2C%22orgType%22%3A0%2C%22language%22%3A%22zh_CN%22%2C%22pl+atForm%22%3A%22platform%22%2C%22appId%22%3A%22yb-home%22%2C%22systemCode%22%3A%22yb-home%22%2C%22couponGlobalParam%22%3Anull%7D)&alias=comm-pro&method=getGeneralUsableCouponListByPage&param=%7B%22userInfo%22%3A+%7B%22pin%22%3A%22{pin}%22%2C%22userLevel%22%3A0%2C%22totalScore%22%3A0%2C%22flagInfo+%22%3Anull%7D%2C%22couponTypeList%22%3Anull%2C%22couponStyleList%22%3Anull%2C%22sortColumnDict%22%3A%22BY_CREATE_TIME%22%2C%22sortTypeDict%22%3A%22BY_DESC%22%2C%22states%22%3Anull%2C%22ext%22%3Anull%7D%2C+%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%7D%2C%7B%22channel%22%3A1%2C%22systemName%22%3A%22yb-home%22%2C%22ip%22%3A%22125.69.161.71%22%2C%22country%22%3A%22country%22%2C%22orgType%22%3A0%2C%22language%22%3A%22zh_CN%22%2C%22pl+atForm%22%3A%22platform%22%2C%22appId%22%3A%22yb-home%22%2C%22systemCode%22%3A%22yb-home%22%2C%22couponGlobalParam%22%3Anull%7D&invokeBy=telnet",
            timeout=30,
        )
        return response.text
    except Exception as e:
        return f"查询失败: {str(e)}"


def getGeneralDisableCouponListByPage(session, pin):
    """已使用券查询"""

    # 发送请求
    response = session.get(
        f"http://jsf.yibin.jdxpoc.com/iface/manage/invoke/362161?interfaceName=com.jd.mycoupon.client.service.MyCouponGeneralService&ip=***********&port=22000&safVer=210&token=&cmd=invoke+com.jd.mycoupon.client.service.MyCouponGeneralService.getGeneralDisableCouponListByPage(%7B%22userInfo%22%3A+%7B%22pin%22%3A%22{pin}%22%2C%22userLevel%22%3A0%2C%22totalScore%22%3A0%2C%22flagInfo+%22%3Anull%7D%2C%22couponTypeList%22%3Anull%2C%22couponStyleList%22%3Anull%2C%22sortColumnDict%22%3A%22BY_CREATE_TIME%22%2C%22sortTypeDict%22%3A%22BY_DESC%22%2C%22states%22%3Anull%2C%22ext%22%3Anull%7D%2C+%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%7D%2C%7B%22channel%22%3A1%2C%22systemName%22%3A%22yb-home%22%2C%22ip%22%3A%22125.69.161.71%22%2C%22country%22%3A%22country%22%2C%22orgType%22%3A0%2C%22language%22%3A%22zh_CN%22%2C%22pl+atForm%22%3A%22platform%22%2C%22appId%22%3A%22yb-home%22%2C%22systemCode%22%3A%22yb-home%22%2C%22couponGlobalParam%22%3Anull%7D)&alias=comm-pro&method=getGeneralDisableCouponListByPage&param=%7B%22userInfo%22%3A+%7B%22pin%22%3A%22{pin}%22%2C%22userLevel%22%3A0%2C%22totalScore%22%3A0%2C%22flagInfo+%22%3Anull%7D%2C%22couponTypeList%22%3Anull%2C%22couponStyleList%22%3Anull%2C%22sortColumnDict%22%3A%22BY_CREATE_TIME%22%2C%22sortTypeDict%22%3A%22BY_DESC%22%2C%22states%22%3Anull%2C%22ext%22%3Anull%7D%2C+%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%7D%2C%7B%22channel%22%3A1%2C%22systemName%22%3A%22yb-home%22%2C%22ip%22%3A%22125.69.161.71%22%2C%22country%22%3A%22country%22%2C%22orgType%22%3A0%2C%22language%22%3A%22zh_CN%22%2C%22pl+atForm%22%3A%22platform%22%2C%22appId%22%3A%22yb-home%22%2C%22systemCode%22%3A%22yb-home%22%2C%22couponGlobalParam%22%3Anull%7D&invokeBy=telnet"
    )
    return response.text


def getGeneralExpireCouponListByPage(session, pin):
    """已过期券查询"""

    # 发送请求
    response = session.get(
        f"http://jsf.yibin.jdxpoc.com/iface/manage/invoke/362161?interfaceName=com.jd.mycoupon.client.service.MyCouponGeneralService&ip=***********&port=22000&safVer=210&token=&cmd=invoke+com.jd.mycoupon.client.service.MyCouponGeneralService.getGeneralExpireCouponListByPage(%7B%22userInfo%22%3A+%7B%22pin%22%3A%22{pin}%22%2C%22userLevel%22%3A0%2C%22totalScore%22%3A0%2C%22flagInfo+%22%3Anull%7D%2C%22couponTypeList%22%3Anull%2C%22couponStyleList%22%3Anull%2C%22sortColumnDict%22%3A%22BY_CREATE_TIME%22%2C%22sortTypeDict%22%3A%22BY_DESC%22%2C%22states%22%3Anull%2C%22ext%22%3Anull%7D%2C+%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%7D%2C%7B%22channel%22%3A1%2C%22systemName%22%3A%22yb-home%22%2C%22ip%22%3A%22125.69.161.71%22%2C%22country%22%3A%22country%22%2C%22orgType%22%3A0%2C%22language%22%3A%22zh_CN%22%2C%22pl+atForm%22%3A%22platform%22%2C%22appId%22%3A%22yb-home%22%2C%22systemCode%22%3A%22yb-home%22%2C%22couponGlobalParam%22%3Anull%7D)&alias=comm-pro&method=getGeneralExpireCouponListByPage&param=%7B%22userInfo%22%3A+%7B%22pin%22%3A%22{pin}%22%2C%22userLevel%22%3A0%2C%22totalScore%22%3A0%2C%22flagInfo+%22%3Anull%7D%2C%22couponTypeList%22%3Anull%2C%22couponStyleList%22%3Anull%2C%22sortColumnDict%22%3A%22BY_CREATE_TIME%22%2C%22sortTypeDict%22%3A%22BY_DESC%22%2C%22states%22%3Anull%2C%22ext%22%3Anull%7D%2C+%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%7D%2C%7B%22channel%22%3A1%2C%22systemName%22%3A%22yb-home%22%2C%22ip%22%3A%22125.69.161.71%22%2C%22country%22%3A%22country%22%2C%22orgType%22%3A0%2C%22language%22%3A%22zh_CN%22%2C%22pl+atForm%22%3A%22platform%22%2C%22appId%22%3A%22yb-home%22%2C%22systemCode%22%3A%22yb-home%22%2C%22couponGlobalParam%22%3Anull%7D&invokeBy=telnet"
    )
    return response.text


def query_juan_by_pin(session, pin):
    try:
        result = {}
        usable = getGeneralUsableCouponListByPage(session, pin)
        disable = getGeneralDisableCouponListByPage(session, pin)
        expire = getGeneralExpireCouponListByPage(session, pin)
        result["usable"] = format_json_data(json.loads(usable))
        result["disable"] = format_json_data(json.loads(disable))
        result["expire"] = format_json_data(json.loads(expire))

        # 格式化结果为字符串
        formatted_result = f"可用优惠券: {result["usable"]}\n失效优惠券: {result["disable"]}\n过期优惠券: {result["expire"]}"

        return {
            "success": True,
            "message": "查询成功",
            "raw_response": formatted_result,
        }
    except Exception as e:
        return {"success": False, "message": f"查询失败: {str(e)}", "raw_response": ""}


def login_to_system(username, password):
    """
    登录第三方系统

    参数:
        username (str): 用户名
        password (str): 密码

    返回:
        dict: 登录结果，包含session信息
    """
    # 1. 创建会话
    session = requests.Session()
    login_url = "http://jsf.yibin.jdxpoc.com/login/login"

    # 2. 获取动态参数
    login_page = session.get(login_url)
    viewstate = "/wEPDwUKLTQ2ODYwNTE3NmRkIbNHY3T5Mqr6jz6ORILXKQAAAAA="
    event_validation = (
        "/wEWBAKno/TmDAKbufQdAtLF4JEPAu/P+d0FCpA84heSt2TTTEI8kk7OLwAAAAA="
    )

    # 3. 密码加密（这里用MD5示例）
    encrypted_pwd = hashlib.md5(password.encode()).hexdigest()

    # 4. 提交登录
    login_data = {
        "__VIEWSTATE": viewstate,
        "__EVENTVALIDATION": event_validation,
        "name": username,
        "password": encrypted_pwd,  # 使用加密后的密码
        "Logon": "登  录",
    }
    response = session.post(login_url, data=login_data)

    # 检查登录是否成功 - 更准确的判断方式
    if response.status_code == 200:
        # 检查响应内容，如果包含登录表单，说明登录失败
        if (
            "请录入用户名" in response.text
            or "请录入密码" in response.text
            or 'name="password"' in response.text
        ):
            return {
                "success": False,
                "message": "登录失败: 用户名或密码错误",
                "session": None,
            }
        else:
            # 登录成功
            return {"success": True, "message": "登录成功", "session": session}
    else:
        return {
            "success": False,
            "message": f"登录失败: 状态码 {response.status_code}",
            "session": None,
        }


def query_user_auth(session, name, phone, card):
    """
    查询用户实名认证

    参数:
        session (requests.Session): 登录会话对象
        name (str): 用户姓名
        phone (str): 手机号
        card (str): 身份证号

    返回:
        dict: 查询结果（原始响应内容）
    """
    try:
        # 构建查询URL
        url = f"http://jsf.yibin.jdxpoc.com/iface/manage/invoke/364946?interfaceName=com.jd.jnos.framework.idaas.api.UserCertificationJsfService&ip=**********&port=22000&safVer=210&token=comm-pro1-xkh7j&cmd=invoke+com.jd.jnos.framework.idaas.api.UserCertificationJsfService.queryUserCertification(%22{phone}%22%2C%22{name}%22%2C%22{card}%22)&alias=comm-pro&method=queryUserCertification&param=%22{phone}%22%2C%22{name}%22%2C%22{card}%22&invokeBy=telnet"

        # 设置请求头以支持跨域
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

        response = session.get(url, headers=headers, timeout=30)

        return {"success": True, "message": "查询成功", "raw_response": response.text}
    except Exception as e:
        return {"success": False, "message": f"查询失败: {str(e)}", "raw_response": ""}


def query_auth_and_coupon(session, name, phone, card):
    """
    查询实名认证和优惠券情况

    参数:
        session (dict): 登录会话信息
        name (str): 用户姓名
        phone (str): 手机号
        idcard (str): 身份证号

    返回:
        dict: 查询结果（原始响应内容）
    """
    try:
        # 第一步：查询用户实名认证
        auth_result = query_user_auth(session, name, phone, card)
        if not auth_result["success"]:
            return auth_result

        # 从实名认证结果中提取PIN
        auth_response = auth_result["raw_response"]
        print("实名认证结果:", auth_response)
        pin_match = re.search(
            r"(?:对应用户pin:\s*([a-zA-Z0-9_]+)|其他账号pin:\s*\[([a-zA-Z0-9_]+)\])",
            auth_response,
            flags=re.IGNORECASE,
        )

        if pin_match:
            pin = pin_match.group(1) or pin_match.group(2)
            print("提取到的 PIN:", pin)

            # 第二步：使用PIN查询优惠券
            coupon_result = query_juan_by_pin(session, pin)

            # 合并结果
            combined_response = f"=== 实名认证结果 ===\n{auth_response}\n\n=== 优惠券查询结果 ===\n{coupon_result.get('raw_response', '查询失败')}"

            return {
                "success": True,
                "message": "查询成功",
                "raw_response": combined_response,
            }
        else:
            return {
                "success": False,
                "message": "未能从实名认证结果中提取到PIN",
                "raw_response": auth_response,
            }

    except Exception as e:
        return {"success": False, "message": f"查询失败: {str(e)}", "raw_response": ""}


def query_account_by_phone(session, phone):
    try:
        response = session.get(
            f"http://jsf.yibin.jdxpoc.com/iface/manage/invoke/362983?interfaceName=com.jd.mini.user.soa.export.MiniUserExport&ip=**********&port=22000&safVer=210&token=&cmd=invoke+com.jd.mini.user.soa.export.MiniUserExport.getUserInfoByMobile(%7B+%22appId%22%3A%22idaas%22%2C+%22traceId%22%3A%22T70155269354924755142%22%2C+%22clientIp%22%3A%22127.0.0.1%22%2C+%22tenantCode%22%3A%22yibin0%22%2C+%22mobile%22%3A%22{phone}%22+%7D)&alias=cb-pro&method=getUserInfoByMobile&param=%7B+%22appId%22%3A%22idaas%22%2C+%22traceId%22%3A%22T70155269354924755142%22%2C+%22clientIp%22%3A%22127.0.0.1%22%2C+%22tenantCode%22%3A%22yibin0%22%2C+%22mobile%22%3A%22{phone}%22+%7D&invokeBy=telnet"
        )
        return {"success": True, "message": "查询成功", "raw_response": response.text}
    except Exception as e:
        return {"success": False, "message": f"查询失败: {str(e)}", "raw_response": ""}


def query_account_by_pin(session, pin):
    """
    通过PIN查询账号信息

    参数:
        session (requests.Session): 登录会话对象
        pin (str): 用户PIN

    返回:
        dict: 查询结果（原始响应内容）
    """
    try:
        response = session.get(
            f"http://jsf.yibin.jdxpoc.com/iface/manage/invoke/362983?interfaceName=com.jd.mini.user.soa.export.MiniUserExport&ip=**********&port=22000&safVer=210&token=&cmd=invoke+com.jd.mini.user.soa.export.MiniUserExport.getUserInfoByPin(%7B+%22appId%22%3A%22idaas%22%2C+%22traceId%22%3A%22T61531999670554401494%22%2C+%22clientIp%22%3A%22127.0.0.1%22%2C+%22tenantCode%22%3A%22yibin0%22%2C+%22pin%22%3A%22{pin}%22+%7D)&alias=cb-pro&method=getUserInfoByPin&param=%7B+%22appId%22%3A%22idaas%22%2C+%22traceId%22%3A%22T61531999670554401494%22%2C+%22clientIp%22%3A%22127.0.0.1%22%2C+%22tenantCode%22%3A%22yibin0%22%2C+%22pin%22%3A%22{pin}%22+%7D&invokeBy=telnet"
        )
        return {"success": True, "message": "查询成功", "raw_response": response.text}
    except Exception as e:
        return {"success": False, "message": f"查询失败: {str(e)}", "raw_response": ""}


def batch_query_phone_by_pin(
    session, txt_file_content, return_stats_only=False, query_id=None
):
    """批量PIN查询手机号"""
    try:
        pins = [line.strip() for line in txt_file_content.split("\n") if line.strip()]

        if not pins:
            return {
                "success": False,
                "message": "文件中没有找到有效的PIN",
                "raw_response": "",
            }

        if return_stats_only:
            total_count = len(pins)
            estimated_time = total_count * (TIME_SLEEP + 0.5)
            return {
                "success": True,
                "stats_only": True,
                "total_count": total_count,
                "estimated_time": estimated_time,
                "message": f"准备处理 {total_count} 个PIN，预估耗时 {estimated_time:.1f} 秒",
            }

        results = []
        success_count = 0

        # 创建输出文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pin_{timestamp}.txt"
        filepath = os.path.join("downloads", filename)

        # 确保输出目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # 注册查询
        if query_id:
            register_query(query_id, "pin", len(pins))

        # 写入文件头
        headers = [
            f"批量PIN查询结果 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"总计查询: {len(pins)} 个PIN",
            "-" * 50,
            "PIN,手机号",
        ]
        write_query_result(filepath, "\n".join(headers))

        for pin in pins:
            # 检查是否需要中断
            if query_id and is_query_aborted(query_id):
                write_query_result(filepath, "\n--- 查询已中断 ---")
                return {
                    "success": False,
                    "message": "查询已被中断",
                    "raw_response": "\n".join(results),
                    "results": results,
                    "filename": filename,
                    "aborted": True,
                }

            try:
                # 查询账号信息
                query_result = query_account_by_pin(session, pin)
                time.sleep(TIME_SLEEP)

                if query_result["success"]:
                    response_text = query_result["raw_response"]
                    try:
                        # 解析JSON响应
                        response_json = json.loads(response_text)
                        content_str = response_json["content"]

                        # 提取手机号
                        mobile_pattern = r'"mobile":"(\d+)"'
                        mobile_matches = re.findall(mobile_pattern, content_str)

                        if mobile_matches:
                            mobile = mobile_matches[0]
                            result_line = f"{pin},{mobile}"
                            success_count += 1
                        else:
                            result_line = f"{pin},未找到手机号"

                    except (json.JSONDecodeError, KeyError) as e:
                        result_line = f"{pin},解析失败({str(e)})"
                else:
                    result_line = f"{pin},查询失败({query_result['message']})"

            except Exception as e:
                result_line = f"{pin},查询异常({str(e)})"

            # 实时写入结果
            print(f"正在处理: {result_line}")  # 打印进度
            write_query_result(filepath, result_line)
            results.append(result_line)

            # 更新进度
            if query_id:
                update_query_progress(query_id)

        # 完成后清理查询状态
        if query_id:
            remove_query(query_id)

        return {
            "success": True,
            "message": f"批量查询完成，共处理 {len(results)} 个PIN，成功 {success_count} 个。结果已保存到文件: {filename}",
            "raw_response": "\n".join(results),
            "results": results,
            "filename": filename,
            "aborted": False,
        }

    except Exception as e:
        # 确保在发生异常时也清理查询状态
        if query_id:
            remove_query(query_id)
        return {
            "success": False,
            "message": f"批量查询失败: {str(e)}",
            "raw_response": "",
            "aborted": False,
        }


def query_ip_from_api(ip):
    """从指定API查询IP归属地"""
    import requests

    try:
        response = requests.get(
            f"https://ip.zxinc.org/api.php?type=json&ip={ip}", timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            if data.get("data") and data.get("data").get("location"):
                location = data.get("data").get("location").replace("\t", " ").strip()
                return location if location else "未知地区"
    except Exception as e:
        print(f"查询IP {ip} 失败: {str(e)}")

    return "未知地区"


def query_ip_location(csv_file_content, return_stats_only=False):
    """
    IP归属地查询

    参数:
        csv_file_content (str): CSV文件内容
        return_stats_only (bool): 是否只返回统计信息

    返回:
        dict: 查询结果或统计信息
    """
    import datetime
    import time
    import csv
    import io

    try:
        # 解析CSV文件内容
        lines = [line.strip() for line in csv_file_content.split("\n") if line.strip()]

        if not lines:
            return {
                "success": False,
                "message": "文件中没有找到有效的数据",
                "raw_response": "",
            }

        # 解析CSV格式，提取IP地址
        # 尝试不同的分隔符和列名
        ip_addresses = []

        # 尝试制表符分隔
        try:
            csv_reader = csv.DictReader(io.StringIO(csv_file_content), delimiter="\t")
            for row in csv_reader:
                if "customerip" in row and row["customerip"].strip():
                    ip = row["customerip"].strip()
                    ip_addresses.append(ip)
        except:
            pass

        # 如果制表符分隔没有找到数据，尝试逗号分隔
        if not ip_addresses:
            try:
                csv_reader = csv.DictReader(
                    io.StringIO(csv_file_content), delimiter=","
                )
                for row in csv_reader:
                    if "customerip" in row and row["customerip"].strip():
                        ip = row["customerip"].strip()
                        ip_addresses.append(ip)
            except:
                pass

        # 如果还是没有数据，尝试直接按行解析（假设IP在第二列）
        if not ip_addresses:
            try:
                lines = csv_file_content.strip().split("\n")
                for i, line in enumerate(lines):
                    if i == 0:  # 跳过标题行
                        continue
                    parts = line.split("\t") if "\t" in line else line.split(",")
                    if len(parts) >= 2 and parts[1].strip():  # 第二列（B列）
                        ip = parts[1].strip()
                        ip_addresses.append(ip)
            except:
                pass

        if not ip_addresses:
            return {
                "success": False,
                "message": "文件中没有找到有效的IP地址",
                "raw_response": "",
            }

        # 如果只需要统计信息，立即返回
        if return_stats_only:
            total_count = len(ip_addresses)
            estimated_time = total_count * 0.5  # 每个IP预估处理时间0.5秒
            return {
                "success": True,
                "stats_only": True,
                "total_count": total_count,
                "estimated_time": estimated_time,
                "message": f"准备处理 {total_count} 个IP，预估耗时 {estimated_time:.1f} 秒",
            }

        results = []
        success_count = 0

        for i, ip in enumerate(ip_addresses, 1):
            try:
                # 查询IP归属地
                location = query_ip_from_api(ip)
                result_line = f"{ip},{location}"
                results.append(result_line)
                success_count += 1

                print(f"进度: {i}/{len(ip_addresses)} - {ip} -> {location}")

                # 添加小延迟避免请求过快
                time.sleep(0.2)

            except Exception as e:
                result_line = f"{ip},查询异常({str(e)})"
                results.append(result_line)
                print(f"查询失败: {ip} - {str(e)}")

        # 创建输出文件夹
        output_dir = "downloads"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 生成带时间戳的文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ip_location_{timestamp}.csv"
        filepath = os.path.join(output_dir, filename)

        # 写入结果文件
        try:
            with open(filepath, "w", encoding="utf-8", newline="") as f:
                # 写入CSV头部
                f.write("IP地址,归属地\n")
                # 写入查询结果
                for result in results:
                    f.write(result + "\n")

            file_message = f"结果已保存到文件: {filename}"
        except Exception as e:
            file_message = f"文件保存失败: {str(e)}"

        return {
            "success": True,
            "message": f"IP归属地查询完成，共处理 {len(ip_addresses)} 个IP，成功 {success_count} 个。{file_message}",
            "raw_response": "\n".join(results),
            "results": results,
            "filename": filename,
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"IP归属地查询失败: {str(e)}",
            "raw_response": "",
        }


def add_merchant_to_whitelist(session, merchant_info):
    """
    添加商家到消费券白名单

    参数:
        session (dict): 登录会话信息
        merchant_info (dict): 商家信息

    返回:
        dict: 操作结果（原始响应内容）
    """
    # TODO: 在这里实现您的商家白名单添加逻辑
    # 返回完整的API响应内容，不需要解析
    result = {
        "success": True,
        "message": "商家已成功添加到白名单",
        "raw_response": f"商家白名单添加结果 - 商家ID: {merchant_info.get('merchant_id', 'N/A')}, 商家名称: {merchant_info.get('merchant_name', 'N/A')}, Session Token: {session.get('token', 'N/A')}",
    }
    return result


# 全局变量存储活跃查询
active_queries = {}
query_lock = Lock()


class QueryStatus:
    def __init__(self, query_type, total_count):
        self.query_id = None
        self.query_type = query_type  # 'pin' 或 'ip'
        self.start_time = datetime.datetime.now()
        self.total_count = total_count
        self.processed_count = 0
        self.is_aborted = False

    def to_dict(self):
        return {
            "queryId": self.query_id,
            "queryType": self.query_type,
            "startTime": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "totalCount": self.total_count,
            "processedCount": self.processed_count,
            "isAborted": self.is_aborted,
        }


def register_query(query_id, query_type, total_count):
    """注册新的查询"""
    with query_lock:
        status = QueryStatus(query_type, total_count)
        status.query_id = query_id
        active_queries[query_id] = status
        return status


def update_query_progress(query_id, increment=1):
    """更新查询进度"""
    with query_lock:
        if query_id in active_queries:
            active_queries[query_id].processed_count += increment


def remove_query(query_id):
    """移除查询"""
    with query_lock:
        if query_id in active_queries:
            del active_queries[query_id]


def abort_query(query_id):
    """中断查询"""
    with query_lock:
        if query_id in active_queries:
            active_queries[query_id].is_aborted = True
            return True
    return False


def is_query_aborted(query_id):
    """检查查询是否被中断"""
    with query_lock:
        return query_id in active_queries and active_queries[query_id].is_aborted


def get_active_queries():
    """获取所有活跃查询"""
    with query_lock:
        return [query.to_dict() for query in active_queries.values()]


def write_query_result(filepath, content):
    """写入查询结果到文件"""
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, "a", encoding="utf-8") as f:
        f.write(content + "\n")
        f.flush()


def batch_query_ip_location(
    session, txt_file_content, query_id=None, return_stats_only=False
):
    """批量查询IP归属地"""
    try:
        ips = [line.strip() for line in txt_file_content.split("\n") if line.strip()]

        if not ips:
            return {
                "success": False,
                "message": "文件中没有找到有效的IP",
                "raw_response": "",
            }

        if return_stats_only:
            total_count = len(ips)
            estimated_time = total_count * (TIME_SLEEP + 0.5)
            return {
                "success": True,
                "stats_only": True,
                "total_count": total_count,
                "estimated_time": estimated_time,
                "message": f"准备处理 {total_count} 个IP，预估耗时 {estimated_time:.1f} 秒",
            }

        results = []
        success_count = 0

        # 创建输出文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ip_{timestamp}.txt"
        filepath = os.path.join("downloads", filename)

        # 注册查询
        if query_id:
            register_query(query_id, "ip", len(ips))

        # 写入文件头
        headers = [
            f"批量IP归属地查询结果 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"总计查询: {len(ips)} 个IP",
            "-" * 50,
            "IP,归属地",
        ]
        write_query_result(filepath, "\n".join(headers))

        for ip in ips:
            # 检查是否需要中断
            if query_id and is_query_aborted(query_id):
                write_query_result(filepath, "\n--- 查询已中断 ---")
                return {
                    "success": False,
                    "message": "查询已被中断",
                    "raw_response": "\n".join(results),
                    "results": results,
                    "filename": filename,
                    "aborted": True,
                }

            try:
                result = query_ip_location(session, ip)
                time.sleep(TIME_SLEEP)

                if result["success"]:
                    location = result.get("location", "未知")
                    result_line = f"{ip},{location}"
                    success_count += 1
                else:
                    result_line = f"{ip},查询失败({result['message']})"

            except Exception as e:
                result_line = f"{ip},查询异常({str(e)})"

            results.append(result_line)
            write_query_result(filepath, result_line)

            if query_id:
                update_query_progress(query_id)

        if query_id:
            remove_query(query_id)

        return {
            "success": True,
            "message": f"批量查询完成，共处理 {len(ips)} 个IP，成功 {success_count} 个。结果已保存到文件: {filename}",
            "raw_response": "\n".join(results),
            "results": results,
            "filename": filename,
        }

    except Exception as e:
        if query_id:
            remove_query(query_id)
        return {
            "success": False,
            "message": f"批量查询失败: {str(e)}",
            "raw_response": "",
        }
