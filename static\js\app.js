// 全局变量存储文件内容
let batchPinFileContent = ''
let ipLocationFileContent = ''

// 全局变量存储当前查询ID
let currentQueryId = null

// 生成唯一查询ID
function generateQueryId() {
  return 'q_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 创建批量PIN查询页面
function createBatchPinQueryPage() {
  return `
        <div class="query-form">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>批量PIN查询手机号
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label">上传TXT文件</label>
                        <div class="upload-area" id="batch-pin-upload" onclick="document.getElementById('batch-pin-file').click()">
                            <i class="fas fa-cloud-upload-alt fa-2x mb-2" style="color: var(--primary-color);"></i>
                            <h6>拖拽文件到此处或点击选择文件</h6>
                            <p class="text-muted mb-0 small">支持TXT格式，每行一个PIN</p>
                        </div>
                        <input type="file" id="batch-pin-file" accept=".txt" style="display: none;" onchange="handleBatchPinUpload(this)">
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button class="btn btn-primary me-3" id="batch-pin-query-btn" onclick="startBatchPinQuery()" disabled>
                        <i class="fas fa-play me-2"></i>开始查询
                    </button>
                    <button class="btn btn-success" onclick="showPinDownloadList()">
                        <i class="fas fa-download me-2"></i>查看下载列表
                    </button>
                </div>
            </div>
        </div>
        <div id="batch-pin-progress-area"></div>
        <div id="batch-pin-query-result"></div>
        <!-- 在query-form中添加状态提示区域 -->
        <div id="query-status" class="mt-3" style="display: none;">
            <div class="alert alert-info">
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-spinner fa-spin me-2"></i>查询正在进行中...</span>
                    <button class="btn btn-sm btn-warning" onclick="abortQuery()">
                        <i class="fas fa-stop me-1"></i>中断查询
                    </button>
                </div>
            </div>
        </div>
    `
}

// 创建IP归属地查询页面
function createIpLocationQueryPage() {
  return `
        <div class="query-form">
            <div class="card-header">
                <i class="fas fa-map-marker-alt me-2"></i>IP归属地查询
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label">上传CSV文件</label>
                        <div class="upload-area" id="ip-location-upload" onclick="document.getElementById('ip-location-file').click()">
                            <i class="fas fa-cloud-upload-alt fa-2x mb-2" style="color: var(--primary-color);"></i>
                            <h6>拖拽文件到此处或点击选择文件</h6>
                            <p class="text-muted mb-0 small">支持CSV格式</p>
                        </div>
                        <input type="file" id="ip-location-file" accept=".csv" style="display: none;" onchange="handleIpLocationUpload(this)">
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button class="btn btn-primary me-3" id="ip-location-query-btn" onclick="startIpLocationQuery()" disabled>
                        <i class="fas fa-play me-2"></i>开始查询
                    </button>
                    <button class="btn btn-success" onclick="showIpDownloadList()">
                        <i class="fas fa-download me-2"></i>查看下载列表
                    </button>
                </div>
            </div>
        </div>
        <div id="ip-location-progress-area"></div>
        <div id="ip-location-query-result"></div>
    `
}

// 创建消费券商家白名单页面
function createCouponWhitelistPage() {
  return `
        <div class="query-form">
            <div class="card-header">
                <i class="fas fa-store me-2"></i>消费券商家白名单
            </div>
            <div class="card-body">
                <div class="welcome-card">
                    <div class="feature-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>功能开发中</h3>
                    <p>消费券商家白名单管理功能正在开发中，敬请期待</p>
                </div>
            </div>
        </div>
    `
}

// 查询用户实名认证
async function queryUserAuth() {
  const name = document.getElementById('auth-name-input').value
  const phone = document.getElementById('auth-phone-input').value
  const idcard = document.getElementById('auth-idcard-input').value

  if (!name || !phone || !idcard) {
    showAlert('请填写完整的用户信息（姓名、手机号、身份证号）', 'warning')
    return
  }

  showLoading('auth-query-result')

  try {
    const response = await fetch('/api/query_user_auth', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, phone, idcard }),
    })

    const result = await response.json()
    displayQueryResult('auth-query-result', result)
  } catch (error) {
    showAlert(`查询出错: ${error.message}`, 'danger')
  }
}

// 查询实名认证和优惠券
async function queryAuthCoupon() {
  const name = document.getElementById('auth-coupon-name-input').value
  const phone = document.getElementById('auth-coupon-phone-input').value
  const idcard = document.getElementById('auth-coupon-idcard-input').value

  if (!name || !phone || !idcard) {
    showAlert('请填写完整的用户信息（姓名、手机号、身份证号）', 'warning')
    return
  }

  showLoading('auth-coupon-query-result')

  try {
    const response = await fetch('/api/query_auth_coupon', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, phone, idcard }),
    })

    const result = await response.json()
    displayQueryResult('auth-coupon-query-result', result)
  } catch (error) {
    showAlert(`查询出错: ${error.message}`, 'danger')
  }
}

// PIN查询账号
async function queryPinAccount() {
  const pin = document.getElementById('pin-input').value

  if (!pin) {
    showAlert('请输入用户PIN', 'warning')
    return
  }

  showLoading('pin-query-result')

  try {
    const response = await fetch('/api/query_pin_account', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ pin }),
    })

    const result = await response.json()
    displayQueryResult('pin-query-result', result)
  } catch (error) {
    showAlert(`查询出错: ${error.message}`, 'danger')
  }
}

// 手机号查询账号
async function queryPhoneAccount() {
  const phone = document.getElementById('phone-input').value

  if (!phone) {
    showAlert('请输入手机号', 'warning')
    return
  }

  showLoading('phone-query-result')

  try {
    const response = await fetch('/api/query_phone_account', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phone }),
    })

    const result = await response.json()
    displayQueryResult('phone-query-result', result)
  } catch (error) {
    showAlert(`查询出错: ${error.message}`, 'danger')
  }
}

// 清空输入框函数
function clearAuthInputs() {
  document.getElementById('auth-name-input').value = ''
  document.getElementById('auth-phone-input').value = ''
  document.getElementById('auth-idcard-input').value = ''
}

function clearAuthCouponInputs() {
  document.getElementById('auth-coupon-name-input').value = ''
  document.getElementById('auth-coupon-phone-input').value = ''
  document.getElementById('auth-coupon-idcard-input').value = ''
}

function clearPinInput() {
  document.getElementById('pin-input').value = ''
}

function clearPhoneInput() {
  document.getElementById('phone-input').value = ''
}

// 显示加载状态
function showLoading(elementId) {
  const element = document.getElementById(elementId)
  if (element) {
    element.innerHTML = `
            <div class="text-center py-5">
                <div class="loading mb-3"></div>
                <p class="text-muted">正在查询中，请稍候...</p>
            </div>
        `
  }
}

// 显示警告信息
function showAlert(message, type = 'info') {
  const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `

  // 找到当前活跃的结果区域
  let resultArea
  switch (currentMenu) {
    case 'user-auth-query':
      resultArea = document.getElementById('auth-query-result')
      break
    case 'auth-coupon-query':
      resultArea = document.getElementById('auth-coupon-query-result')
      break
    case 'pin-to-account':
      resultArea = document.getElementById('pin-query-result')
      break
    case 'phone-to-account':
      resultArea = document.getElementById('phone-query-result')
      break
    default:
      resultArea = document.getElementById('main-content')
  }

  if (resultArea) {
    resultArea.innerHTML = alertHtml
  }
}

// 获取警告图标
function getAlertIcon(type) {
  switch (type) {
    case 'success':
      return 'check-circle'
    case 'warning':
      return 'exclamation-triangle'
    case 'danger':
      return 'times-circle'
    case 'info':
      return 'info-circle'
    default:
      return 'info-circle'
  }
}

// 显示查询结果
function displayQueryResult(elementId, result) {
  const element = document.getElementById(elementId)
  if (!element) return

  if (result.success) {
    element.innerHTML = `
            <div class="result-area mt-4">
                <div class="card-header">
                    <i class="fas fa-check-circle me-2"></i>查询结果
                </div>
                <div class="card-body">
                    <pre>${result.raw_response || '无响应内容'}</pre>
                </div>
            </div>
        `
  } else {
    element.innerHTML = `
            <div class="alert alert-danger mt-4">
                <i class="fas fa-times-circle me-2"></i>
                <strong>查询失败：</strong>${result.message}
            </div>
        `
  }
}

// 处理批量PIN文件上传
async function handleBatchPinUpload(input) {
  const file = input.files[0]
  if (!file) return

  if (!file.name.toLowerCase().endsWith('.txt')) {
    showAlert('请上传TXT文件', 'warning')
    return
  }

  const formData = new FormData()
  formData.append('file', file)

  try {
    const response = await fetch('/api/batch_pin_upload', {
      method: 'POST',
      body: formData,
    })

    const result = await response.json()

    if (result.success) {
      batchPinFileContent = result.file_content
      document.getElementById('batch-pin-query-btn').disabled = false

      // 更新上传区域显示
      const uploadArea = document.getElementById('batch-pin-upload')
      uploadArea.innerHTML = `
                <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                <h6 class="text-success">${result.message}</h6>
                <p class="text-muted mb-0 small">点击重新选择文件</p>
            `
    } else {
      showAlert(result.message, 'danger')
    }
  } catch (error) {
    showAlert(`文件上传失败: ${error.message}`, 'danger')
  }
}

// 中断查询
async function abortQuery(queryIdToAbort = null) {
  const idToAbort = queryIdToAbort || currentQueryId
  if (idToAbort) {
    console.log('正在中断查询:', idToAbort)
    try {
      const response = await fetch('/api/abort_query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query_id: idToAbort }),
      })
      const result = await response.json()
      if (result.success) {
        // 显示中断成功提示
        const progressContainer = document.getElementById('progress-container')
        if (progressContainer) {
          progressContainer.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-circle me-2"></i>查询已被手动中断
                        </div>
                    `
        }

        // 重置查询状态
        resetQueryStatus()
      }
    } catch (error) {
      console.error('中断查询失败:', error)
      showAlert('中断查询失败，请重试', 'danger')
    }
  }
}

// 重置查询状态
function resetQueryStatus() {
  currentQueryId = null
  const queryBtn = document.getElementById('batch-pin-query-btn')
  if (queryBtn) {
    queryBtn.disabled = false
    queryBtn.innerHTML = '<i class="fas fa-play me-2"></i>开始查询'
  }
}

// 开始批量PIN查询
async function startBatchPinQuery() {
  if (!batchPinFileContent) {
    showAlert('请先上传文件', 'warning')
    return
  }

  // 生成新的查询ID
  currentQueryId = generateQueryId()

  const queryBtn = document.getElementById('batch-pin-query-btn')
  const progressArea = document.getElementById('batch-pin-progress-area')
  const resultArea = document.getElementById('batch-pin-query-result')

  // 显示进度信息
  queryBtn.disabled = true
  queryBtn.innerHTML = '<span class="loading me-2"></span>处理中...'

  try {
    // 先获取统计信息
    const statsResponse = await fetch('/api/batch_pin_stats', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ file_content: batchPinFileContent }),
    })

    const statsResult = await statsResponse.json()

    if (statsResult.success) {
      // 从 statsResult 中获取总时间和总数量
      const totalTime = statsResult.estimated_time
      const totalCount = statsResult.total_count
      let remainingTime = totalTime

      // 显示查询状态和中断按钮
      progressArea.innerHTML = `
        <div class="alert alert-info mt-4" id="progress-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="alert-heading mb-0">
                    <i class="fas fa-rocket me-2"></i>查询已开始
                </h6>
                <button class="btn btn-sm btn-warning" onclick="abortQuery('${currentQueryId}')">
                    <i class="fas fa-stop me-1"></i>中断查询
                </button>
            </div>
            <p class="mb-2"><i class="fas fa-chart-bar me-2"></i>${statsResult.message}</p>
            <p class="mb-3 small" id="remaining-time">
                <i class="fas fa-clock me-2"></i>预计剩余时间：<span id="time-counter">${Math.ceil(remainingTime)}</span> 秒
            </p>
            <div class="progress mb-3" style="height: 12px;">
                <div class="progress-bar progress-bar-striped progress-bar-animated" id="progress-bar"
                     style="width: 0%"></div>
            </div>
            <p class="mb-2 small" id="progress-text">
                <i class="fas fa-tasks me-2"></i>进度：<span id="progress-counter">0</span> / ${totalCount} (<span id="progress-percentage">0</span>%)
            </p>
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>系统正在后台处理，可随时点击中断按钮停止查询
            </small>
        </div>
    `

      // 启动进度更新定时器
      const startTime = Date.now()
      let progressTimer
      let isQueryComplete = false

      // 模拟进度更新 - 使用更保守的进度计算
      const updateProgress = () => {
        if (isQueryComplete) return

        const elapsed = (Date.now() - startTime) / 1000

        // 进度计算策略：
        // 0%-90%根据时间比例计算
        // 90%-95%缓慢增长（为超时预留空间）
        let currentProgress = 0

        if (elapsed <= totalTime) {
          // 正常时间范围内：0-90%线性增长
          currentProgress = (elapsed / totalTime) * 90
        } else {
          // 超过预估时间：90% + 缓慢增长到95%
          const overtime = elapsed - totalTime
          const overtimeProgress = Math.min(5, (overtime / totalTime) * 10)
          currentProgress = 90 + overtimeProgress
        }

        // 确保进度不超过95%（留5%给完成状态）
        currentProgress = Math.min(95, currentProgress)

        remainingTime = Math.max(0, totalTime - elapsed)

        // 更新进度条
        const progressBar = document.getElementById('progress-bar')
        const timeCounter = document.getElementById('time-counter')
        const progressCounter = document.getElementById('progress-counter')
        const progressPercentage = document.getElementById('progress-percentage')

        if (progressBar && timeCounter && progressCounter && progressPercentage) {
          progressBar.style.width = `${currentProgress.toFixed(1)}%`

          // 如果超过预估时间，显示"处理中..."
          if (elapsed > totalTime) {
            timeCounter.textContent = '处理中...'
          } else {
            timeCounter.textContent = Math.ceil(remainingTime)
          }

          // 根据进度计算已处理数量
          const processedCount = Math.floor((currentProgress / 100) * totalCount)
          progressCounter.textContent = processedCount

          // 更新百分比显示
          progressPercentage.textContent = Math.floor(currentProgress)
        }
      }

      // 立即更新一次，然后每秒更新
      updateProgress()
      progressTimer = setInterval(updateProgress, 1000)

      // 执行实际查询
      const queryResponse = await fetch('/api/batch_pin_query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ file_content: batchPinFileContent, query_id: currentQueryId }),
      })

      // 查询完成后清除定时器
      isQueryComplete = true
      clearInterval(progressTimer)

      const queryResult = await queryResponse.json()
      const actualTime = ((Date.now() - startTime) / 1000).toFixed(1)

      // 更新进度条为100%完成状态
      const progressBar = document.getElementById('progress-bar')
      const timeCounter = document.getElementById('time-counter')
      const progressCounter = document.getElementById('progress-counter')
      const progressPercentage = document.getElementById('progress-percentage')

      if (progressBar && timeCounter && progressCounter && progressPercentage) {
        progressBar.style.width = '100%'
        progressBar.classList.remove('progress-bar-animated')
        progressBar.classList.add('bg-success')
        timeCounter.textContent = '0'
        progressCounter.textContent = totalCount
        progressPercentage.textContent = '100'
      }

      // 延迟一秒后显示结果，让用户看到100%完成
      setTimeout(() => {
        // 清空进度区域
        progressArea.innerHTML = ''

        // 重置下载列表状态
        pinShowingDownloadList = false
        pinQueryResultContent = ''

        if (queryResult.success) {
          resultArea.innerHTML = `
                        <div class="alert alert-success mt-4">
                            <h6 class="alert-heading mb-3">
                                <i class="fas fa-check-circle me-2"></i>查询完成！
                            </h6>
                            <p class="mb-2">
                                <i class="fas fa-chart-line me-2"></i>总计处理 ${totalCount} 个PIN，实际用时 ${actualTime} 秒
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>${queryResult.message}
                            </p>
                        </div>
                        <div class="result-area mt-4">
                            <div class="card-header">
                                <i class="fas fa-list me-2"></i>查询结果详情
                            </div>
                            <div class="card-body">
                                <pre style="max-height: 400px; overflow-y: auto;">${queryResult.raw_response}</pre>
                            </div>
                        </div>
                    `
        } else {
          resultArea.innerHTML = `
                        <div class="alert alert-danger mt-4">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>查询失败：</strong>${queryResult.message}
                        </div>
                    `
        }
      }, 1000)
    } else {
      progressArea.innerHTML = ''
      showAlert(`获取统计信息失败: ${statsResult.message}`, 'danger')
    }
  } catch (error) {
    // 清理定时器
    if (typeof progressTimer !== 'undefined') {
      clearInterval(progressTimer)
    }
    progressArea.innerHTML = ''
    showAlert(`查询出错: ${error.message}`, 'danger')
  } finally {
    resetQueryStatus()
  }
}

// 全局变量存储查询结果内容
let pinQueryResultContent = ''
let pinShowingDownloadList = false

// 显示PIN下载列表
async function showPinDownloadList() {
  try {
    const resultArea = document.getElementById('batch-pin-query-result')

    // 如果当前显示的是下载列表，切换回查询结果
    if (pinShowingDownloadList) {
      resultArea.innerHTML = pinQueryResultContent
      pinShowingDownloadList = false
      return
    }

    // 保存当前的查询结果内容
    pinQueryResultContent = resultArea.innerHTML

    const response = await fetch('/api/get_recent_files?type=pin&limit=10')
    const result = await response.json()

    if (result.success) {
      // 首先检查是否有活跃的查询
      const hasActiveQuery = result.activeQuery && result.activeQuery.queryId

      let fileListHtml = `
                <div class="result-area mt-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><i class="fas fa-download me-2"></i>最近的PIN查询结果文件</span>
                            <button class="btn btn-sm btn-outline-secondary" onclick="showPinDownloadList()">
                                <i class="fas fa-arrow-left me-1"></i>返回查询结果
                            </button>
                        </div>
                        ${
                          hasActiveQuery
                            ? `
                        <div class="alert alert-info py-2 mb-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="me-3"><i class="fas fa-spinner fa-spin me-2"></i>查询正在进行中</span>
                                    <small class="text-muted">开始时间: ${result.activeQuery.startTime}</small>
                                </div>
                                <button class="btn btn-warning btn-sm" onclick="abortQuery('${result.activeQuery.queryId}')">
                                    <i class="fas fa-stop me-1"></i>中断查询
                                </button>
                            </div>
                        </div>
                        `
                            : ''
                        }
                    </div>
                    <div class="card-body">
                        <div class="list-group">
            `

      result.files.forEach((file) => {
        fileListHtml += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>${file.filename}
                                </h6>
                                <p class="mb-0 text-muted small">
                                    <i class="fas fa-tag me-1"></i>类型: ${file.type} |
                                    <i class="fas fa-weight me-1"></i>大小: ${file.size} 字节 |
                                    <i class="fas fa-clock me-1"></i>时间: ${file.modified}
                                </p>
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="downloadFile('${file.filename}')">
                                <i class="fas fa-download me-1"></i>下载
                            </button>
                        </div>
                    </div>
                `
      })

      fileListHtml += `
                        </div>
                    </div>
                </div>
            `

      resultArea.innerHTML = fileListHtml
      pinShowingDownloadList = true
    } else {
      const noFilesHtml = `
                <div class="alert alert-info mt-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-info-circle me-2"></i>暂无查询结果文件</span>
                        <button class="btn btn-sm btn-outline-secondary" onclick="showPinDownloadList()">
                            <i class="fas fa-arrow-left me-1"></i>返回查询结果
                        </button>
                    </div>
                </div>
            `
      resultArea.innerHTML = noFilesHtml
      pinShowingDownloadList = true
    }
  } catch (error) {
    showAlert(`获取文件列表失败: ${error.message}`, 'danger')
  }
}

// 处理IP归属地文件上传
async function handleIpLocationUpload(input) {
  const file = input.files[0]
  if (!file) return

  if (!file.name.toLowerCase().endsWith('.csv')) {
    showAlert('请上传CSV文件', 'warning')
    return
  }

  const formData = new FormData()
  formData.append('file', file)

  try {
    const response = await fetch('/api/ip_location_upload', {
      method: 'POST',
      body: formData,
    })

    const result = await response.json()

    if (result.success) {
      ipLocationFileContent = result.file_content
      document.getElementById('ip-location-query-btn').disabled = false

      // 更新上传区域显示
      const uploadArea = document.getElementById('ip-location-upload')
      uploadArea.innerHTML = `
                <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                <h6 class="text-success">${result.message}</h6>
                <p class="text-muted mb-0 small">点击重新选择文件</p>
            `
    } else {
      showAlert(result.message, 'danger')
    }
  } catch (error) {
    showAlert(`文件上传失败: ${error.message}`, 'danger')
  }
}

// 开始IP归属地查询
async function startIpLocationQuery() {
  if (!ipLocationFileContent) {
    showAlert('请先上传文件', 'warning')
    return
  }

  // 生成新的查询ID
  currentQueryId = generateQueryId()

  const queryBtn = document.getElementById('ip-location-query-btn')
  const progressArea = document.getElementById('ip-location-progress-area')
  const resultArea = document.getElementById('ip-location-query-result')

  queryBtn.disabled = true
  queryBtn.innerHTML = '<span class="loading me-2"></span>处理中...'

  try {
    // 先获取统计信息
    const statsResponse = await fetch('/api/ip_location_stats', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ file_content: ipLocationFileContent }),
    })

    const statsResult = await statsResponse.json()

    if (statsResult.success) {
      // 显示初始进度条
      const totalTime = statsResult.estimated_time
      const totalCount = statsResult.total_count
      let remainingTime = totalTime

      progressArea.innerHTML = `
                <div class="alert alert-info mt-4" id="ip-progress-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="alert-heading mb-0">
                            <i class="fas fa-rocket me-2"></i>查询已开始
                        </h6>
                        <button class="btn btn-sm btn-warning" onclick="abortQuery()">
                            <i class="fas fa-stop me-1"></i>中断查询
                        </button>
                    </div>
                    <p class="mb-2"><i class="fas fa-chart-bar me-2"></i>${statsResult.message}</p>
                    <p class="mb-3 small" id="ip-remaining-time">
                        <i class="fas fa-clock me-2"></i>预计剩余时间：<span id="ip-time-counter">${Math.ceil(remainingTime)}</span> 秒
                    </p>
                    <div class="progress mb-3" style="height: 12px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" id="ip-progress-bar"
                             style="width: 0%"></div>
                    </div>
                    <p class="mb-2 small" id="ip-progress-text">
                        <i class="fas fa-tasks me-2"></i>进度：<span id="ip-progress-counter">0</span> / ${totalCount} (<span id="ip-progress-percentage">0</span>%)
                    </p>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>系统正在后台处理，可随时点击中断按钮停止查询
                    </small>
                </div>
            `

      // 启动进度更新定时器
      const startTime = Date.now()
      let ipProgressTimer
      let isIpQueryComplete = false

      // IP查询进度更新
      const updateIpProgress = () => {
        if (isIpQueryComplete) return

        const elapsed = (Date.now() - startTime) / 1000

        let currentProgress = 0

        if (elapsed <= totalTime) {
          // 正常时间范围内：0-90%线性增长
          currentProgress = (elapsed / totalTime) * 90
        } else {
          // 超过预估时间：90% + 缓慢增长到95%
          const overtime = elapsed - totalTime
          const overtimeProgress = Math.min(5, (overtime / totalTime) * 10)
          currentProgress = 90 + overtimeProgress
        }

        currentProgress = Math.min(95, currentProgress)
        remainingTime = Math.max(0, totalTime - elapsed)

        // 更新进度条
        const progressBar = document.getElementById('ip-progress-bar')
        const timeCounter = document.getElementById('ip-time-counter')
        const progressCounter = document.getElementById('ip-progress-counter')
        const progressPercentage = document.getElementById('ip-progress-percentage')

        if (progressBar && timeCounter && progressCounter && progressPercentage) {
          progressBar.style.width = `${currentProgress.toFixed(1)}%`

          if (elapsed > totalTime) {
            timeCounter.textContent = '处理中...'
          } else {
            timeCounter.textContent = Math.ceil(remainingTime)
          }

          const processedCount = Math.floor((currentProgress / 100) * totalCount)
          progressCounter.textContent = processedCount
          progressPercentage.textContent = Math.floor(currentProgress)
        }
      }

      updateIpProgress()
      ipProgressTimer = setInterval(updateIpProgress, 1000)

      // 执行实际查询
      const queryResponse = await fetch('/api/ip_location_query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          file_content: ipLocationFileContent,
          query_id: currentQueryId,
        }),
      })

      // 查询完成后清除定时器
      isIpQueryComplete = true
      clearInterval(ipProgressTimer)

      const queryResult = await queryResponse.json()
      const actualTime = ((Date.now() - startTime) / 1000).toFixed(1)

      // 更新进度条为100%完成状态
      const progressBar = document.getElementById('ip-progress-bar')
      const timeCounter = document.getElementById('ip-time-counter')
      const progressCounter = document.getElementById('ip-progress-counter')
      const progressPercentage = document.getElementById('ip-progress-percentage')

      if (progressBar && timeCounter && progressCounter && progressPercentage) {
        progressBar.style.width = '100%'
        progressBar.classList.remove('progress-bar-animated')
        progressBar.classList.add('bg-success')
        timeCounter.textContent = '0'
        progressCounter.textContent = totalCount
        progressPercentage.textContent = '100'
      }

      // 延迟一秒后显示结果
      setTimeout(() => {
        progressArea.innerHTML = ''

        // 重置下载列表状态
        ipShowingDownloadList = false
        ipQueryResultContent = ''

        if (queryResult.success) {
          resultArea.innerHTML = `
                        <div class="alert alert-success mt-4">
                            <h6 class="alert-heading mb-3">
                                <i class="fas fa-check-circle me-2"></i>查询完成！
                            </h6>
                            <p class="mb-2">
                                <i class="fas fa-chart-line me-2"></i>总计处理 ${totalCount} 个IP，实际用时 ${actualTime} 秒
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>${queryResult.message}
                            </p>
                        </div>
                        <div class="result-area mt-4">
                            <div class="card-header">
                                <i class="fas fa-list me-2"></i>查询结果详情
                            </div>
                            <div class="card-body">
                                <pre style="max-height: 400px; overflow-y: auto;">${queryResult.raw_response || '无响应内容'}</pre>
                            </div>
                        </div>
                    `
        } else {
          resultArea.innerHTML = `
                        <div class="alert alert-danger mt-4">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>查询失败：</strong>${queryResult.message}
                        </div>
                    `
        }
      }, 1000)
    } else {
      progressArea.innerHTML = ''
      showAlert(`获取统计信息失败: ${statsResult.message}`, 'danger')
    }
  } catch (error) {
    // 清理定时器
    if (typeof ipProgressTimer !== 'undefined') {
      clearInterval(ipProgressTimer)
    }
    progressArea.innerHTML = ''
    showAlert(`查询出错: ${error.message}`, 'danger')
  } finally {
    queryBtn.disabled = false
    queryBtn.innerHTML = '<i class="fas fa-play me-2"></i>开始查询'
  }
}

// 全局变量存储IP查询结果内容
let ipQueryResultContent = ''
let ipShowingDownloadList = false

// 显示IP下载列表
async function showIpDownloadList() {
  try {
    const resultArea = document.getElementById('ip-location-query-result')

    // 如果当前显示的是下载列表，切换回查询结果
    if (ipShowingDownloadList) {
      resultArea.innerHTML = ipQueryResultContent
      ipShowingDownloadList = false
      return
    }

    // 保存当前的查询结果内容
    ipQueryResultContent = resultArea.innerHTML

    const response = await fetch('/api/get_recent_files?type=ip&limit=10')
    const result = await response.json()

    if (result.success) {
      // 首先检查是否有活跃的查询
      const hasActiveQuery = result.activeQuery && result.activeQuery.queryId

      let fileListHtml = `
                <div class="result-area mt-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><i class="fas fa-download me-2"></i>最近的IP查询结果文件</span>
                            <button class="btn btn-sm btn-outline-secondary" onclick="showIpDownloadList()">
                                <i class="fas fa-arrow-left me-1"></i>返回查询结果
                            </button>
                        </div>
                        ${
                          hasActiveQuery
                            ? `
                        <div class="alert alert-info py-2 mb-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="me-3"><i class="fas fa-spinner fa-spin me-2"></i>查询正在进行中</span>
                                    <small class="text-muted">开始时间: ${result.activeQuery.startTime}</small>
                                </div>
                                <button class="btn btn-warning btn-sm" onclick="abortQuery('${result.activeQuery.queryId}')">
                                    <i class="fas fa-stop me-1"></i>中断查询
                                </button>
                            </div>
                        </div>
                        `
                            : ''
                        }
                    </div>
                    <div class="card-body">
                        <div class="list-group">
            `

      result.files.forEach((file) => {
        fileListHtml += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>${file.filename}
                                </h6>
                                <p class="mb-0 text-muted small">
                                    <i class="fas fa-tag me-1"></i>类型: ${file.type} |
                                    <i class="fas fa-weight me-1"></i>大小: ${file.size} 字节 |
                                    <i class="fas fa-clock me-1"></i>时间: ${file.modified}
                                </p>
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="downloadFile('${file.filename}')">
                                <i class="fas fa-download me-1"></i>下载
                            </button>
                        </div>
                    </div>
                `
      })

      fileListHtml += `
                        </div>
                    </div>
                </div>
            `

      resultArea.innerHTML = fileListHtml
      ipShowingDownloadList = true
    } else {
      const noFilesHtml = `
                <div class="alert alert-info mt-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-info-circle me-2"></i>暂无查询结果文件</span>
                        <button class="btn btn-sm btn-outline-secondary" onclick="showIpDownloadList()">
                            <i class="fas fa-arrow-left me-1"></i>返回查询结果
                        </button>
                    </div>
                </div>
            `
      resultArea.innerHTML = noFilesHtml
      ipShowingDownloadList = true
    }
  } catch (error) {
    showAlert(`获取文件列表失败: ${error.message}`, 'danger')
  }
}

// 下载文件
function downloadFile(filename) {
  window.open(`/api/download_file/${filename}`, '_blank')
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function () {
  // 添加拖拽上传功能
  setupDragAndDrop()
})

// 设置拖拽上传功能
function setupDragAndDrop() {
  // 为所有上传区域添加拖拽功能
  document.addEventListener('dragover', function (e) {
    e.preventDefault()
  })

  document.addEventListener('drop', function (e) {
    e.preventDefault()

    const uploadArea = e.target.closest('.upload-area')
    if (!uploadArea) return

    const files = e.dataTransfer.files
    if (files.length === 0) return

    const file = files[0]

    // 根据上传区域ID处理不同类型的文件
    if (uploadArea.id === 'batch-pin-upload') {
      if (file.name.toLowerCase().endsWith('.txt')) {
        const input = document.getElementById('batch-pin-file')
        input.files = files
        handleBatchPinUpload(input)
      } else {
        showAlert('请上传TXT文件', 'warning')
      }
    } else if (uploadArea.id === 'ip-location-upload') {
      if (file.name.toLowerCase().endsWith('.csv')) {
        const input = document.getElementById('ip-location-file')
        input.files = files
        handleIpLocationUpload(input)
      } else {
        showAlert('请上传CSV文件', 'warning')
      }
    }
  })

  // 添加拖拽视觉反馈
  document.addEventListener('dragenter', function (e) {
    const uploadArea = e.target.closest('.upload-area')
    if (uploadArea) {
      uploadArea.classList.add('dragover')
    }
  })

  document.addEventListener('dragleave', function (e) {
    const uploadArea = e.target.closest('.upload-area')
    if (uploadArea && !uploadArea.contains(e.relatedTarget)) {
      uploadArea.classList.remove('dragover')
    }
  })
}

// 页面加载时检查是否有正在进行的查询
async function checkActiveQueries() {
  try {
    const response = await fetch('/api/check_active_queries')
    const result = await response.json()

    if (result.success && result.activeQueries && result.activeQueries.length > 0) {
      const activeQuery = result.activeQueries[0] // 获取最新的查询
      showActiveQueryNotification(activeQuery)
    }
  } catch (error) {
    console.error('检查活跃查询失败:', error)
  }
}

// 显示活跃查询通知
function showActiveQueryNotification(queryInfo) {
  const resultArea = document.getElementById('batch-pin-query-result')
  if (resultArea) {
    resultArea.innerHTML = `
      <div class="alert alert-info mt-4">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>检测到正在进行的查询</h6>
            <p class="mb-0 small">开始时间: ${queryInfo.startTime}</p>
          </div>
          <div>
            <button class="btn btn-primary btn-sm me-2" onclick="showPinDownloadList()">
              <i class="fas fa-download me-1"></i>查看下载列表
            </button>
            <button class="btn btn-warning btn-sm" onclick="abortQuery('${queryInfo.queryId}')">
              <i class="fas fa-stop me-1"></i>中断查询
            </button>
          </div>
        </div>
      </div>
    `
  }
}

// 处理路由变化
async function handleRouteChange() {
  if (currentQueryId) {
    console.log('页面切换，正在中断查询...', currentQueryId)
    try {
      await abortQuery(currentQueryId)
      console.log('查询已中断')
    } catch (error) {
      console.error('中断查询失败:', error)
    } finally {
      // 重置状态
      currentQueryId = null
    }
  }
}

// 处理页面关闭
function handleBeforeUnload(event) {
  if (currentQueryId) {
    // 显示确认对话框（部分浏览器只显示默认提示）
    event.preventDefault()
    event.returnValue = '查询正在进行中，确定要离开页面吗？'

    // 发送同步请求确保在页面关闭前完成中断操作
    try {
      var xhr = new XMLHttpRequest()
      xhr.open('POST', '/api/abort_query', false) // 同步请求
      xhr.setRequestHeader('Content-Type', 'application/json')
      xhr.send(JSON.stringify({ query_id: currentQueryId }))
      // 等待一段时间确保请求完成
      var start = Date.now()
      while (Date.now() - start < 300) {}
    } catch (error) {
      // 记录但不阻断
      console.error('中断查询失败:', error)
    }
    // 必须重置状态（即使失败）
    currentQueryId = null
    return event.returnValue
  }
}

// 处理标签页可见性变化
function handleVisibilityChange() {
  // 标签页隐藏时不再中断查询
  if (document.hidden && currentQueryId) {
    console.log('标签页隐藏，查询继续进行...')
  }
}

// 设置菜单切换监听
function setupMenuListeners() {
  const menuItems = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]')
  menuItems.forEach((item) => {
    item.addEventListener('click', function (e) {
      if (currentQueryId) {
        e.preventDefault() // 阻止默认的标签页切换
        // 只弹一次确认
        if (window.confirm('查询正在进行中，切换页面将中断查询。是否继续？')) {
          try {
            // 同步XHR中断
            var xhr = new XMLHttpRequest()
            xhr.open('POST', '/api/abort_query', false)
            xhr.setRequestHeader('Content-Type', 'application/json')
            xhr.send(JSON.stringify({ query_id: currentQueryId }))
            // 等待一段时间确保请求完成
            var start = Date.now()
            while (Date.now() - start < 200) {}
            // 重置状态
            currentQueryId = null
            // 手动切换到目标标签页
            var targetId = this.getAttribute('data-bs-target')
            document.querySelector('.nav-link.active').classList.remove('active')
            document.querySelector('.tab-pane.active').classList.remove('active', 'show')
            this.classList.add('active')
            document.querySelector(targetId).classList.add('active', 'show')
          } catch (error) {
            console.error('切换页面时中断查询失败:', error)
            showAlert('切换页面时中断查询失败，请重试', 'danger')
          }
        }
      }
    })
  })
}

// 页面加载完成后初始化事件监听
document.addEventListener('DOMContentLoaded', function () {
  // 设置页面关闭事件监听
  window.addEventListener('beforeunload', handleBeforeUnload)

  // 设置标签页可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 设置菜单切换监听
  setupMenuListeners()

  // 其他初始化...
  setupDragAndDrop()
  checkActiveQueries()
})
