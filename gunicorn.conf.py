# Gunicorn配置文件 - 海狸拾袋管理工具

import multiprocessing
import os

# 服务器套接字
bind = "0.0.0.0:8050"
backlog = 2048

# 工作进程
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# 重启
max_requests = 1000
max_requests_jitter = 100
preload_app = True

# 调试
reload = False
daemon = False

# 进程命名
proc_name = "haili-web"

# 用户和组
user = None  # 在systemd服务中设置
group = None  # 在systemd服务中设置

# 路径
tmp_upload_dir = None

# 日志
errorlog = "-"  # 输出到stderr
loglevel = "info"
accesslog = "-"  # 输出到stdout
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# SSL (如果需要)
keyfile = None
certfile = None

# 其他
forwarded_allow_ips = "*"
secure_scheme_headers = {
    'X-FORWARDED-PROTOCOL': 'ssl',
    'X-FORWARDED-PROTO': 'https',
    'X-FORWARDED-SSL': 'on'
}

# 钩子函数
def on_starting(server):
    """服务器启动时调用"""
    server.log.info("海狸拾袋管理工具正在启动...")

def on_reload(server):
    """重新加载时调用"""
    server.log.info("海狸拾袋管理工具正在重新加载...")

def worker_int(worker):
    """工作进程收到SIGINT信号时调用"""
    worker.log.info("工作进程 %s 收到中断信号", worker.pid)

def pre_fork(server, worker):
    """工作进程fork之前调用"""
    server.log.info("工作进程 %s 即将启动", worker.pid)

def post_fork(server, worker):
    """工作进程fork之后调用"""
    server.log.info("工作进程 %s 已启动", worker.pid)

def post_worker_init(worker):
    """工作进程初始化完成后调用"""
    worker.log.info("工作进程 %s 初始化完成", worker.pid)

def worker_abort(worker):
    """工作进程异常退出时调用"""
    worker.log.info("工作进程 %s 异常退出", worker.pid)

def pre_exec(server):
    """执行新的主进程之前调用"""
    server.log.info("执行新的主进程...")

def when_ready(server):
    """服务器准备就绪时调用"""
    server.log.info("海狸拾袋管理工具已准备就绪，监听 %s", server.address)

def on_exit(server):
    """服务器退出时调用"""
    server.log.info("海狸拾袋管理工具正在关闭...")

# 环境变量
raw_env = [
    'FLASK_ENV=production',
]
