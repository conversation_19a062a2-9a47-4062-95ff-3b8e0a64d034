#!/bin/bash
# 海狸拾袋管理工具 - 快速安装脚本 (CentOS 8)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查操作系统
check_os() {
    if [[ ! -f /etc/redhat-release ]]; then
        print_error "此脚本仅支持CentOS/RHEL系统"
        exit 1
    fi
    
    local version=$(cat /etc/redhat-release)
    print_info "检测到系统: $version"
}

# 安装系统依赖
install_system_deps() {
    print_info "安装系统依赖..."
    
    sudo dnf update -y
    sudo dnf install python3 python3-pip python3-devel -y
    sudo dnf groupinstall "Development Tools" -y
    sudo dnf install openssl-devel libffi-devel -y
    
    print_success "系统依赖安装完成"
}

# 创建项目目录
setup_project_dir() {
    local project_dir="/opt/haili_web"
    
    print_info "设置项目目录: $project_dir"
    
    if [[ ! -d "$project_dir" ]]; then
        sudo mkdir -p "$project_dir"
        sudo chown $USER:$USER "$project_dir"
        print_success "项目目录创建完成"
    else
        print_warning "项目目录已存在"
    fi
    
    cd "$project_dir"
}

# 创建虚拟环境
setup_venv() {
    print_info "创建Python虚拟环境..."
    
    if [[ ! -d ".venv" ]]; then
        python3 -m venv .venv
        source .venv/bin/activate
        pip install --upgrade pip
        print_success "虚拟环境创建完成"
    else
        print_warning "虚拟环境已存在"
        source .venv/bin/activate
    fi
}

# 安装Python依赖
install_python_deps() {
    print_info "安装Python依赖..."
    
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
        pip install gunicorn  # 生产环境服务器
        print_success "Python依赖安装完成"
    else
        print_error "requirements.txt文件不存在"
        exit 1
    fi
}

# 创建必要目录
create_dirs() {
    print_info "创建必要目录..."
    
    local dirs=("query_results" "downloads" "logs")
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_info "创建目录: $dir"
        fi
    done
    
    chmod 755 query_results downloads logs
    print_success "目录创建完成"
}

# 创建系统用户
create_user() {
    print_info "创建系统用户..."
    
    if ! id "haili" &>/dev/null; then
        sudo useradd -r -s /bin/false haili
        print_success "用户haili创建完成"
    else
        print_warning "用户haili已存在"
    fi
    
    sudo chown -R haili:haili /opt/haili_web
}

# 安装systemd服务
install_service() {
    print_info "安装systemd服务..."
    
    if [[ -f "haili-web.service" ]]; then
        sudo cp haili-web.service /etc/systemd/system/
        sudo systemctl daemon-reload
        sudo systemctl enable haili-web
        print_success "systemd服务安装完成"
    else
        print_warning "haili-web.service文件不存在，跳过服务安装"
    fi
}

# 配置防火墙
setup_firewall() {
    print_info "配置防火墙..."
    
    if systemctl is-active --quiet firewalld; then
        sudo firewall-cmd --permanent --add-port=8050/tcp
        sudo firewall-cmd --reload
        print_success "防火墙配置完成"
    else
        print_warning "firewalld未运行，跳过防火墙配置"
    fi
}

# 运行验证
run_verification() {
    print_info "运行启动验证..."
    
    if [[ -f "启动验证.py" ]]; then
        python3 启动验证.py
        if [[ $? -eq 0 ]]; then
            print_success "验证通过"
        else
            print_error "验证失败，请检查配置"
            exit 1
        fi
    else
        print_warning "启动验证脚本不存在"
    fi
}

# 主函数
main() {
    echo "🚀 海狸拾袋管理工具 - 快速安装脚本"
    echo "=================================="
    
    check_root
    check_os
    
    print_info "开始安装..."
    
    install_system_deps
    setup_project_dir
    setup_venv
    install_python_deps
    create_dirs
    create_user
    install_service
    setup_firewall
    run_verification
    
    echo ""
    echo "🎉 安装完成！"
    echo "=================================="
    echo "📋 后续步骤:"
    echo "1. 编辑配置文件: vim /opt/haili_web/config.py"
    echo "2. 启动服务: sudo systemctl start haili-web"
    echo "3. 查看状态: sudo systemctl status haili-web"
    echo "4. 访问应用: http://$(hostname -I | awk '{print $1}'):8050"
    echo ""
    echo "📚 相关文档:"
    echo "- 部署说明: 部署说明_CentOS8.md"
    echo "- 服务管理: sudo systemctl {start|stop|restart|status} haili-web"
    echo "- 查看日志: sudo journalctl -u haili-web -f"
}

# 执行主函数
main "$@"
