// 海狸拾袋管理工具 - 主JavaScript文件

// 当前活跃的菜单
let currentMenu = ''

// 切换菜单
async function switchMenu(menuId) {
  // 检查是否有正在进行的查询
  if (window.currentQueryId) {
    const confirmed = confirm('查询正在进行中，切换页面将中断查询。是否继续？')
    if (!confirmed) {
      return // 用户取消切换
    }

    // 中断当前查询
    try {
      await window.abortQuery(window.currentQueryId)
      console.log('菜单切换时查询已中断')
    } catch (error) {
      console.error('菜单切换时中断查询失败:', error)
    }
  }

  // 更新按钮状态
  document.querySelectorAll('.nav-button').forEach((btn) => {
    btn.classList.remove('active')
  })
  document.querySelector(`[data-menu="${menuId}"]`).classList.add('active')

  currentMenu = menuId

  // 加载对应的内容
  loadMenuContent(menuId)
}

// 加载菜单内容
function loadMenuContent(menuId) {
  const mainContent = document.getElementById('main-content')

  switch (menuId) {
    case 'user-auth-query':
      mainContent.innerHTML = createUserAuthQueryPage()
      break
    case 'auth-coupon-query':
      mainContent.innerHTML = createAuthCouponQueryPage()
      break
    case 'pin-to-account':
      mainContent.innerHTML = createPinToAccountPage()
      break
    case 'phone-to-account':
      mainContent.innerHTML = createPhoneToAccountPage()
      break
    case 'batch-pin-query':
      mainContent.innerHTML = createBatchPinQueryPage()
      break
    case 'ip-location-query':
      mainContent.innerHTML = createIpLocationQueryPage()
      break
    case 'coupon-whitelist':
      mainContent.innerHTML = createCouponWhitelistPage()
      break
    default:
      mainContent.innerHTML =
        '<div class="welcome-card"><div class="feature-icon"><i class="fas fa-cog"></i></div><h3>功能开发中</h3><p>该功能正在开发中，敬请期待</p></div>'
  }
}

// 创建用户实名认证查询页面
function createUserAuthQueryPage() {
  return `
        <div class="query-form">
            <div class="card-header">
                <i class="fas fa-id-card me-2"></i>查询用户实名认证
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">姓名</label>
                            <input type="text" class="form-control"
                                   id="auth-name-input" placeholder="请输入真实姓名">
                    </div>
                    <div class="form-group">
                        <label class="form-label">手机号</label>
                            <input type="tel" class="form-control"
                                   id="auth-phone-input" placeholder="请输入手机号">
                    </div>
                    <div class="form-group">
                        <label class="form-label">身份证号</label>
                            <input type="text" class="form-control"
                                   id="auth-idcard-input" placeholder="请输入身份证号">
                    </div>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="queryUserAuth()">
                        <i class="fas fa-search"></i>开始查询
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearAuthInputs()">
                        <i class="fas fa-eraser"></i>清空表单
                    </button>
                </div>
            </div>
        </div>
        <div id="auth-query-result"></div>
    `
}

// 创建实名认证和优惠券查询页面
function createAuthCouponQueryPage() {
  return `
        <div class="query-form">
            <div class="card-header">
                <i class="fas fa-search me-2"></i>查询实名认证和优惠券情况
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">姓名</label>
                        <input type="text" class="form-control" 
                               id="auth-coupon-name-input" placeholder="请输入真实姓名">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">手机号</label>
                        <input type="tel" class="form-control" 
                               id="auth-coupon-phone-input" placeholder="请输入手机号">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">身份证号</label>
                        <input type="text" class="form-control" 
                               id="auth-coupon-idcard-input" placeholder="请输入身份证号">
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button class="btn btn-primary me-3" onclick="queryAuthCoupon()">
                        <i class="fas fa-search me-2"></i>开始查询
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearAuthCouponInputs()">
                        <i class="fas fa-eraser me-2"></i>清空表单
                    </button>
                </div>
            </div>
        </div>
        <div id="auth-coupon-query-result"></div>
    `
}

// 创建PIN查询账号页面
function createPinToAccountPage() {
  return `
        <div class="query-form">
            <div class="card-header">
                <i class="fas fa-user-search me-2"></i>PIN查询账号信息
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label">用户PIN</label>
                        <input type="text" class="form-control" 
                               id="pin-input" placeholder="请输入用户PIN">
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button class="btn btn-primary me-3" onclick="queryPinAccount()">
                        <i class="fas fa-search me-2"></i>开始查询
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearPinInput()">
                        <i class="fas fa-eraser me-2"></i>清空表单
                    </button>
                </div>
            </div>
        </div>
        <div id="pin-query-result"></div>
    `
}

// 创建手机号查询账号页面
function createPhoneToAccountPage() {
  return `
        <div class="query-form">
            <div class="card-header">
                <i class="fas fa-mobile-alt me-2"></i>手机号查询账号
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label">手机号</label>
                        <input type="tel" class="form-control" 
                               id="phone-input" placeholder="请输入手机号">
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button class="btn btn-primary me-3" onclick="queryPhoneAccount()">
                        <i class="fas fa-search me-2"></i>开始查询
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearPhoneInput()">
                        <i class="fas fa-eraser me-2"></i>清空表单
                    </button>
                </div>
            </div>
        </div>
        <div id="phone-query-result"></div>
    `
}
