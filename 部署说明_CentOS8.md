# 海狸拾袋管理工具 - CentOS 8 部署说明

## 系统要求

- **操作系统**: CentOS 8 / RHEL 8 / Rocky Linux 8
- **Python版本**: Python 3.8+
- **内存**: 最低 512MB，推荐 1GB+
- **磁盘空间**: 最低 1GB 可用空间
- **网络**: 需要访问外网（用于IP查询API）

## 1. 系统准备

### 1.1 更新系统

```bash
sudo dnf update -y
```

### 1.2 安装Python 3和pip

```bash
# 安装Python 3.8+
sudo dnf install python3 python3-pip -y

# 验证安装
python3 --version
pip3 --version
```

### 1.3 安装系统依赖

```bash
# 安装开发工具
sudo dnf groupinstall "Development Tools" -y

# 安装其他必要依赖
sudo dnf install python3-devel openssl-devel libffi-devel -y
```

## 2. 项目部署

### 2.1 创建项目目录

```bash
# 创建应用目录
sudo mkdir -p /opt/haili_web
sudo chown $USER:$USER /opt/haili_web
cd /opt/haili_web
```

### 2.2 上传项目文件

将以下文件上传到 `/opt/haili_web/` 目录：

```
haili_web/
├── app_flask.py          # 主应用文件
├── config.py             # 配置文件
├── query_functions.py    # 查询函数
├── requirements.txt      # 依赖包列表
├── templates/
│   └── index.html       # 前端模板
├── static/
│   ├── css/
│   │   └── custom.css   # 样式文件
│   ├── js/
│   │   ├── app.js       # 应用逻辑
│   │   └── main.js      # 主要功能
│   └── vendor/          # 第三方库
└── 部署说明_CentOS8.md   # 本文档
```

### 2.3 创建Python虚拟环境

```bash
cd /opt/haili_web

# 创建虚拟环境
python3 -m venv .venv

# 激活虚拟环境
source .venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 2.4 安装依赖包

```bash
# 安装项目依赖
pip install -r requirements.txt
```

### 2.5 创建必要目录

```bash
# 创建下载目录
mkdir -p downloads

# 设置权限
chmod 755 downloads
```

## 3. 配置应用

### 3.1 配置登录信息

编辑 `config.py` 文件：

```bash
vim config.py
```

修改登录配置：

```python
# 登录配置
LOGIN_CONFIG = {
    "username": "your_username",    # 替换为实际用户名
    "password": "your_password",    # 替换为实际密码
}
```

### 3.2 测试应用

```bash
# 激活虚拟环境（如果未激活）
source .venv/bin/activate

# 运行测试
python3 启动验证.py

# 启动应用（测试模式）
python3 app_flask.py
```

如果看到以下输出，说明启动成功：

```
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8050
 * Running on http://[your-ip]:8050
```

按 `Ctrl+C` 停止测试。

## 4. 生产环境部署

### 4.1 安装Gunicorn

```bash
# 激活虚拟环境
source .venv/bin/activate

# 安装Gunicorn
pip install gunicorn
```

### 4.2 创建Gunicorn配置文件

```bash
vim gunicorn.conf.py
```

添加以下内容：

```python
# Gunicorn配置文件
bind = "0.0.0.0:8050"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
daemon = False
pidfile = "/opt/haili_web/gunicorn.pid"
user = "haili"
group = "haili"
tmp_upload_dir = None
logconfig = None
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
```

### 4.3 创建系统用户

```bash
# 创建专用用户
sudo useradd -r -s /bin/false haili

# 修改项目目录所有者
sudo chown -R haili:haili /opt/haili_web
```

### 4.4 创建systemd服务

```bash
sudo vim /etc/systemd/system/haili-web.service
```

添加以下内容：

```ini
[Unit]
Description=Haili Web Management Tool
After=network.target

[Service]
Type=notify
User=haili
Group=haili
WorkingDirectory=/opt/haili_web
Environment=PATH=/opt/haili_web/.venv/bin
ExecStart=/opt/haili_web/.venv/bin/gunicorn -c gunicorn.conf.py app_flask:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

### 4.5 启动服务

#### 方式一：使用systemd服务（推荐）
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable haili-web

# 启动服务
sudo systemctl start haili-web

# 检查服务状态
sudo systemctl status haili-web
```

#### 方式二：直接启动Gunicorn（不使用systemd）
```bash
# 激活虚拟环境
cd /opt/haili_web
source .venv/bin/activate

# 前台启动（测试用）
gunicorn -c gunicorn.conf.py app_flask:app

# 后台启动（生产用）
nohup gunicorn -c gunicorn.conf.py app_flask:app > logs/gunicorn.log 2>&1 &

# 查看进程
ps aux | grep gunicorn

# 停止服务
pkill -f "gunicorn.*app_flask:app"
```

#### 方式三：使用screen/tmux（持久会话）
```bash
# 使用screen
screen -S haili-web
cd /opt/haili_web
source .venv/bin/activate
gunicorn -c gunicorn.conf.py app_flask:app
# 按 Ctrl+A, D 分离会话

# 重新连接会话
screen -r haili-web

# 使用tmux
tmux new-session -d -s haili-web
tmux send-keys -t haili-web "cd /opt/haili_web && source .venv/bin/activate && gunicorn -c gunicorn.conf.py app_flask:app" Enter

# 查看会话
tmux list-sessions

# 连接会话
tmux attach-session -t haili-web
```

#### 方式四：创建Gunicorn管理脚本
创建一个便于管理的脚本：
```bash
vim /opt/haili_web/gunicorn_manager.sh
```

添加以下内容：
```bash
#!/bin/bash
# Gunicorn管理脚本

WORK_DIR="/opt/haili_web"
VENV_DIR="$WORK_DIR/.venv"
PID_FILE="$WORK_DIR/gunicorn.pid"
LOG_FILE="$WORK_DIR/logs/gunicorn.log"
CONFIG_FILE="$WORK_DIR/gunicorn.conf.py"

cd $WORK_DIR

case "$1" in
    start)
        echo "启动Gunicorn..."
        source $VENV_DIR/bin/activate
        nohup gunicorn -c $CONFIG_FILE app_flask:app > $LOG_FILE 2>&1 &
        echo $! > $PID_FILE
        echo "Gunicorn已启动，PID: $(cat $PID_FILE)"
        ;;
    stop)
        if [ -f $PID_FILE ]; then
            PID=$(cat $PID_FILE)
            echo "停止Gunicorn (PID: $PID)..."
            kill $PID
            rm -f $PID_FILE
            echo "Gunicorn已停止"
        else
            echo "Gunicorn未运行"
        fi
        ;;
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    status)
        if [ -f $PID_FILE ]; then
            PID=$(cat $PID_FILE)
            if ps -p $PID > /dev/null; then
                echo "Gunicorn正在运行 (PID: $PID)"
            else
                echo "PID文件存在但进程未运行"
                rm -f $PID_FILE
            fi
        else
            echo "Gunicorn未运行"
        fi
        ;;
    logs)
        tail -f $LOG_FILE
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs}"
        exit 1
        ;;
esac
```

设置执行权限并使用：
```bash
chmod +x /opt/haili_web/gunicorn_manager.sh

# 启动
./gunicorn_manager.sh start

# 查看状态
./gunicorn_manager.sh status

# 查看日志
./gunicorn_manager.sh logs

# 重启
./gunicorn_manager.sh restart

# 停止
./gunicorn_manager.sh stop
```

## 5. 防火墙配置

### 5.1 开放端口

```bash
# 开放8050端口
sudo firewall-cmd --permanent --add-port=8050/tcp

# 重新加载防火墙配置
sudo firewall-cmd --reload

# 验证端口开放
sudo firewall-cmd --list-ports
```

## 6. Nginx反向代理（可选）

### 6.1 安装Nginx

```bash
sudo dnf install nginx -y
```

### 6.2 配置Nginx

```bash
sudo vim /etc/nginx/conf.d/haili-web.conf
```

添加以下内容：

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为实际域名或IP

    location / {
        proxy_pass http://127.0.0.1:8050;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 静态文件直接由Nginx处理
    location /static/ {
        alias /opt/haili_web/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
```

### 6.3 启动Nginx

```bash
# 测试配置
sudo nginx -t

# 启动Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 开放HTTP端口
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --reload
```

## 7. 日志管理

### 7.1 查看应用日志

```bash
# 查看服务日志
sudo journalctl -u haili-web -f

# 查看最近的日志
sudo journalctl -u haili-web --since "1 hour ago"
```

### 7.2 配置日志轮转

```bash
sudo vim /etc/logrotate.d/haili-web
```

添加以下内容：

```
/var/log/haili-web/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 haili haili
    postrotate
        systemctl reload haili-web
    endscript
}
```

## 8. 维护操作

### 8.1 常用命令

```bash
# 重启服务
sudo systemctl restart haili-web

# 停止服务
sudo systemctl stop haili-web

# 查看服务状态
sudo systemctl status haili-web

# 查看端口占用
sudo netstat -tlnp | grep 8050
```

### 8.2 更新应用

```bash
# 停止服务
sudo systemctl stop haili-web

# 备份当前版本
sudo cp -r /opt/haili_web /opt/haili_web.backup.$(date +%Y%m%d)

# 上传新文件并重启
sudo systemctl start haili-web
```

## 9. 故障排除

### 9.1 常见问题

**问题1**: 服务启动失败

```bash
# 检查日志
sudo journalctl -u haili-web -n 50

# 检查配置文件
python3 -c "import config; print('配置正确')"
```

**问题2**: 端口被占用

```bash
# 查找占用进程
sudo lsof -i :8050

# 杀死进程
sudo kill -9 <PID>
```

**问题3**: 权限问题

```bash
# 修复权限
sudo chown -R haili:haili /opt/haili_web
sudo chmod -R 755 /opt/haili_web
```

### 9.2 性能监控

```bash
# 查看系统资源
htop

# 查看应用进程
ps aux | grep gunicorn

# 查看网络连接
sudo netstat -tlnp | grep 8050
```

## 10. 安全建议

1. **定期更新系统**

   ```bash
   sudo dnf update -y
   ```
2. **配置SSL证书**（如使用Nginx）

   ```bash
   sudo dnf install certbot python3-certbot-nginx -y
   sudo certbot --nginx -d your-domain.com
   ```
3. **限制访问IP**（在防火墙中配置）

   ```bash
   sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='允许的IP' port protocol='tcp' port='8050' accept"
   ```
4. **定期备份**

   ```bash
   # 创建备份脚本
   sudo vim /opt/backup-haili.sh
   ```

## 11. 访问应用

部署完成后，可以通过以下方式访问：

- **直接访问**: http://your-server-ip:8050
- **通过Nginx**: http://your-domain.com

默认功能包括：

- 查询用户实名认证
- 查询实名认证和优惠券情况
- PIN查询账号信息
- 手机号查询账号
- 批量PIN查询手机号
- IP归属地查询
- 消费券商家白名单

## 支持

如有问题，请检查：

1. 服务状态：`sudo systemctl status haili-web`
2. 应用日志：`sudo journalctl -u haili-web -f`
3. 网络连接：`curl http://localhost:8050`

---

**部署完成！** 🎉
