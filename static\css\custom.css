/* 海狸拾袋管理工具 - 主样式文件 */

/* CSS变量定义 - 素雅配色 */
:root {
  --primary-color: #6c757d;
  --primary-gradient: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  --secondary-color: #adb5bd;
  --secondary-gradient: linear-gradient(135deg, #adb5bd 0%, #868e96 100%);
  --success-color: #28a745;
  --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  --warning-color: #ffc107;
  --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  --danger-color: #dc3545;
  --danger-gradient: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
  --dark-color: #343a40;
  --light-color: #f8f9fa;
  --border-radius: 12px;
  --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  line-height: 1.6;
  color: var(--dark-color);
}

/* 侧边栏样式 */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 280px;
  padding: 2rem 1.5rem;
  background: linear-gradient(180deg, #495057 0%, #343a40 100%);
  backdrop-filter: blur(20px);
  border-right: none;
  overflow-y: auto;
  box-shadow: var(--box-shadow);
  z-index: 1000;
}

.sidebar-header {
  text-align: center;
  margin-bottom: 2rem;
}

.sidebar h2 {
  color: white;
  font-weight: 600;
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
}

.sidebar .subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  font-weight: 400;
}

.sidebar hr {
  border-color: rgba(255, 255, 255, 0.2);
  margin: 1.5rem 0;
}

/* 主内容区域 */
.content {
  margin-left: 300px;
  padding: 1.5rem;
  min-height: 100vh;
}

/* 导航按钮 */
.nav-button {
  position: relative;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  transition: var(--transition);
  border-radius: 10px;
  padding: 0.875rem 1rem;
  margin-bottom: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 500;
  font-size: 0.9rem;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-button.active {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 600;
}

.nav-button i {
  width: 20px;
  text-align: center;
  margin-right: 0.75rem;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: var(--box-shadow);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
}

.page-header h3 {
  color: var(--dark-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.4rem;
}

.page-header p {
  color: #6c757d;
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* 查询表单 */
.query-form {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 1.5rem;
  transition: var(--transition);
}

.query-form:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.card-header {
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  font-weight: 600;
  font-size: 1rem;
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.3) 100%);
}

.card-body {
  padding: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

/* 表单控件 */
.form-control {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.6rem 1rem;
  transition: var(--transition);
  background: #f8f9fa;
  font-size: 0.85rem;
  font-weight: 500;
  position: relative;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.15);
  background: white;
  transform: translateY(-1px);
}

.form-control:hover {
  border-color: #ced4da;
  background: white;
}

.form-control::placeholder {
  color: #adb5bd;
  font-weight: 400;
}

.form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  position: relative;
  display: flex;
  align-items: center;
}

.form-label::before {
  content: '';
  width: 4px;
  height: 16px;
  background: var(--primary-gradient);
  border-radius: 2px;
  margin-right: 0.5rem;
}

/* 表单组优化 */
.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* 输入框组合 */
.input-group {
  position: relative;
}

.input-group .form-control {
  padding-left: 3rem;
}

.input-group-text {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--primary-color);
  z-index: 10;
  font-size: 1rem;
}

/* 按钮样式 */
.btn {
  border-radius: 8px;
  padding: 0.6rem 1.5rem;
  font-weight: 600;
  transition: var(--transition);
  border: none;
  font-size: 0.85rem;
  position: relative;
  overflow: hidden;
  text-transform: none;
  letter-spacing: 0.3px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 3px 10px rgba(108, 117, 125, 0.25);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(108, 117, 125, 0.35);
  background: linear-gradient(135deg, #5a6268 0%, #3d4043 100%);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
}

.btn-success {
  background: var(--success-gradient);
  color: white;
  box-shadow: 0 3px 10px rgba(40, 167, 69, 0.25);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.35);
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
}

.btn-outline-secondary {
  border: 2px solid #6c757d;
  color: #6c757d;
  background: transparent;
  position: relative;
}

.btn-outline-secondary::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: #6c757d;
  transition: width 0.3s ease;
  z-index: -1;
}

.btn-outline-secondary:hover::after {
  width: 100%;
}

.btn-outline-secondary:hover {
  color: white;
  border-color: #6c757d;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.25);
}

/* 按钮组 */
.btn-group {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.btn-group .btn {
  min-width: 120px;
}

/* 按钮图标 */
.btn i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

/* 加载状态按钮 */
.btn.loading {
  pointer-events: none;
  opacity: 0.8;
}

.btn.loading i {
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.4s ease-out;
}

/* 按钮悬停效果增强 */
.btn:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.btn:active {
  transform: translateY(0);
}

/* 卡片悬停效果 */
.query-form:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* 结果区域 */
.result-area {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.05);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 上传区域 */
.upload-area {
  border: 2px dashed #dee2e6;
  border-radius: var(--border-radius);
  padding: 1.5rem 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.15);
}

.upload-area.dragover {
  border-color: var(--success-color);
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
  transform: scale(1.01);
}

/* 警告框 */
.alert {
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  font-weight: 500;
  font-size: 0.85rem;
}

.alert-success {
  background: var(--success-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.alert-warning {
  background: var(--warning-gradient);
  color: var(--dark-color);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.alert-danger {
  background: var(--danger-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.alert-info {
  background: var(--secondary-gradient);
  color: var(--dark-color);
  box-shadow: 0 2px 8px rgba(173, 181, 189, 0.2);
}

/* 进度条 */
.progress {
  height: 8px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.progress-bar {
  border-radius: 4px;
  background: var(--success-gradient);
}

/* 列表组 */
.list-group-item {
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 6px !important;
  margin-bottom: 0.5rem;
  transition: var(--transition);
  padding: 0.75rem;
}

.list-group-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 代码块 */
pre {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #e9ecef;
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  color: var(--dark-color);
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  max-width: 100%;
  overflow-x: auto;
}

/* 欢迎卡片 */
.welcome-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 3rem 2.5rem;
  text-align: center;
  box-shadow: var(--box-shadow);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.welcome-card h3 {
  color: var(--dark-color);
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 2rem;
}

.welcome-card p {
  color: #6c757d;
  font-size: 1rem;
}

.feature-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.75rem;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.2);
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-gradient);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: var(--transition);
    width: 280px;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .content {
    margin-left: 0;
    padding: 1rem;
  }

  .page-header {
    padding: 1.5rem;
  }

  .page-header h3 {
    font-size: 1.5rem;
  }

  .card-body {
    padding: 1.25rem;
  }

  .welcome-card {
    padding: 2rem 1.5rem;
  }

  .welcome-card h3 {
    font-size: 1.75rem;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

/* 侧边栏菜单项动画 */
.nav-button {
  position: relative;
  overflow: hidden;
}

.nav-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-button:hover::after {
  left: 100%;
}

/* 上传区域增强效果 */
.upload-area {
  position: relative;
  overflow: hidden;
}

.upload-area::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  pointer-events: none;
}

.upload-area:hover::after {
  width: 200px;
  height: 200px;
}

/* 结果区域滚动条美化 */
.result-area::-webkit-scrollbar {
  width: 6px;
}

.result-area::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.result-area::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
}

/* 加载动画增强 */
.loading {
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 响应式优化 */
@media (max-width: 992px) {
  .content {
    margin-left: 0;
    padding: 1rem;
  }

  .page-header h3 {
    font-size: 1.5rem;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .card-body {
    padding: 1rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .upload-area {
    padding: 2rem 1rem;
  }

  .welcome-card {
    padding: 2rem 1.5rem;
  }

  .welcome-card h3 {
    font-size: 2rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --dark-color: #f8f9fa;
    --light-color: #2c3e50;
  }
}

/* 打印样式 */
@media print {
  .sidebar,
  .btn,
  .upload-area {
    display: none !important;
  }

  .content {
    margin-left: 0 !important;
  }

  .result-area {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .nav-button {
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .form-control {
    border-width: 2px;
  }

  .btn {
    border: 2px solid transparent;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 上传区域字体大小调整 */
.upload-area h6 {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.upload-area .small {
  font-size: 0.75rem;
}

/* 列表项字体大小调整 */
.list-group-item h6 {
  font-size: 0.85rem;
  margin-bottom: 0.25rem;
}

.list-group-item .small {
  font-size: 0.7rem;
}

/* 按钮字体大小调整 */
.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.75rem;
}

/* 结果区域字体调整 */
.result-area .card-header {
  font-size: 0.9rem;
  padding: 0.75rem 1rem;
}

/* 进度信息字体调整 */
.alert h6 {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.alert p {
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
}

.alert .small {
  font-size: 0.7rem;
}

/* 查询结果内容自动换行 */
.result-area .card-body {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
  max-width: 100%;
}

.result-area pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  max-width: 100%;
  overflow-x: auto;
}

/* 警告框内容自动换行 */
.alert {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
  max-width: 100%;
}

/* 列表项内容自动换行 */
.list-group-item {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
  max-width: 100%;
}

.list-group-item h6,
.list-group-item p {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
  max-width: 100%;
}

/* 确保所有文本内容都能自动换行 */
.card-body,
.query-form,
.welcome-card {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

/* 长文本内容处理 */
.card-body p,
.card-body div,
.alert p,
.alert div {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
  max-width: 100%;
}
