<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>海狸拾袋管理工具</title>
    <!-- Bootstrap CSS (本地) -->
    <link href="/static/vendor/bootstrap.min.css" rel="stylesheet" />
    <!-- 自定义样式 -->
    <link href="/static/css/custom.css" rel="stylesheet" />
        <!-- Bootstrap JS (本地) -->
    <script src="/static/vendor/bootstrap.bundle.min.js"></script>
    <!-- 应用JS -->
    <script src="/static/js/app.js"></script>
    <script src="/static/js/main.js"></script>
  </body>
  </head>
  <body>
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2>海狸拾袋</h2>
        <div class="subtitle">管理工具平台</div>
      </div>
      <hr />
      <div class="nav flex-column">
        {% for item in menu_items %}
        <button class="btn nav-button text-start w-100" data-menu="{{ item.id }}" onclick="switchMenu('{{ item.id }}')">
          <i class="{{ item.icon }}"></i>{{ item.label }}
        </button>
        {% endfor %}
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content">
      <!-- 页面头部 -->
      <div class="page-header">
        <h3>海狸拾袋管理工具</h3>
        <p>高效、安全、便捷的数据查询与管理平台</p>
      </div>

      <!-- 登录状态区域 -->
      <div id="login-status-area">
        {% if login_status.success %}
        <div class="alert alert-success">
          <i class="fas fa-check-circle me-2"></i>
          <strong>系统状态：</strong>{{ login_status.message }}
        </div>
        {% else %}
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>系统状态：</strong>{{ login_status.message }}
        </div>
        {% endif %}
      </div>

      <!-- 主内容区域 -->
      <div id="main-content">
        <div class="welcome-card">
          <div class="feature-icon">
            <i class="fas fa-rocket"></i>
          </div>
          <h3>欢迎使用</h3>
          <p>请从左侧菜单中选择您需要使用的功能模块，开始您的数据查询之旅</p>
        </div>
      </div>
    </div>


</html>
