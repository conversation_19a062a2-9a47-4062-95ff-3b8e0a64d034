#!/usr/bin/env python3
"""
Flask应用启动验证脚本
"""
import os
import sys
import importlib.util


def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")

    required_packages = ["flask", "flask_cors", "requests", "jinja2", "werkzeug"]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - 未安装")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False

    print("✅ 所有依赖包已安装")
    return True


def check_files():
    """检查必要文件"""
    print("\n📁 检查项目文件...")

    required_files = [
        "app_flask.py",
        "templates/index.html",
        "static/js/app.js",
        "static/css/custom.css",
        "services.py",
        "config.py",
        "requirements.txt",
    ]

    missing_files = []

    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - 文件不存在")
            missing_files.append(file_path)

    if missing_files:
        print(f"\n❌ 缺少文件: {', '.join(missing_files)}")
        return False

    print("✅ 所有必要文件存在")
    return True


def check_directories():
    """检查目录结构"""
    print("\n📂 检查目录结构...")

    required_dirs = ["templates", "static", "static/js", "static/css", "query_results"]

    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"  ✅ {dir_path}/")
        else:
            print(f"  ⚠️ {dir_path}/ - 目录不存在，正在创建...")
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"  ✅ {dir_path}/ - 已创建")
            except Exception as e:
                print(f"  ❌ {dir_path}/ - 创建失败: {e}")
                return False

    print("✅ 目录结构正确")
    return True


def check_app_import():
    """检查应用导入"""
    print("\n🐍 检查Flask应用...")

    try:
        import app_flask

        print("  ✅ app_flask.py 导入成功")

        # 检查Flask应用对象
        if hasattr(app_flask, "app"):
            print("  ✅ Flask应用对象存在")
        else:
            print("  ❌ Flask应用对象不存在")
            return False

        # 检查路由
        routes = [rule.rule for rule in app_flask.app.url_map.iter_rules()]
        print(f"  ✅ 发现 {len(routes)} 个路由")

        return True

    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        return False


def check_config():
    """检查配置文件"""
    print("\n⚙️ 检查配置...")

    try:
        import config

        print("  ✅ config.py 导入成功")

        if hasattr(config, "LOGIN_CONFIG"):
            login_config = config.LOGIN_CONFIG
            if login_config.get("username") and login_config.get("password"):
                print("  ✅ 登录配置已设置")
            else:
                print("  ⚠️ 登录配置为空，请检查config.py")
        else:
            print("  ❌ LOGIN_CONFIG 不存在")
            return False

        return True

    except Exception as e:
        print(f"  ❌ 配置检查失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Flask应用启动验证")
    print("=" * 50)

    checks = [
        check_dependencies,
        check_directories,
        check_files,
        check_app_import,
        check_config,
    ]

    all_passed = True

    for check in checks:
        if not check():
            all_passed = False

    print("\n" + "=" * 50)

    if all_passed:
        print("🎉 所有检查通过！")
        print("\n📋 启动说明:")
        print("1. 运行命令: python app_flask.py")
        print("2. 打开浏览器访问: http://localhost:8050")
        print("3. 如有问题，请查看项目文档")
        print("\n📚 相关文档:")
        print("- README_Flask.md - 详细说明")
        print("- 快速开始.md - 快速入门")
        print("- 项目完成说明.md - 完整介绍")
    else:
        print("❌ 检查未通过，请解决上述问题后重试")
        sys.exit(1)


if __name__ == "__main__":
    main()
