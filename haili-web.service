[Unit]
Description=Haili Web Management Tool
After=network.target
Wants=network.target

[Service]
Type=notify
User=haili
Group=haili
WorkingDirectory=/opt/haili_web
Environment=PATH=/opt/haili_web/.venv/bin
Environment=FLASK_ENV=production
Environment=PYTHONPATH=/opt/haili_web
ExecStart=/opt/haili_web/.venv/bin/gunicorn -c gunicorn.conf.py app_flask:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=5

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/haili_web/query_results
ReadWritePaths=/opt/haili_web/downloads
ReadWritePaths=/tmp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
