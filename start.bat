@echo off
chcp 65001 >nul
title 海狸拾袋管理工具

echo 🚀 海狸拾袋管理工具启动脚本
echo ================================

REM 检查Python版本
echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查虚拟环境
if exist ".venv" (
    echo 🔧 激活虚拟环境...
    call .venv\Scripts\activate.bat
    if errorlevel 1 (
        echo ❌ 虚拟环境激活失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境已激活
) else (
    echo ⚠️  虚拟环境不存在，使用系统Python
)

REM 运行验证脚本
echo 🔍 运行启动验证...
python 启动验证.py
if errorlevel 1 (
    echo ❌ 启动验证失败，请检查配置
    pause
    exit /b 1
)

echo.
echo 🎉 验证通过！正在启动应用...
echo ================================

REM 启动Flask应用
python app_flask.py

pause
