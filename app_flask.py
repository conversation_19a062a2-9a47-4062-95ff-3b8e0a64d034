"""
海狸拾袋管理工具 - Flask版本
"""

from flask import (
    Flask,
    render_template,
    request,
    jsonify,
    send_file,
    session as flask_session,
)
from flask_cors import CORS
import os
import glob
import base64
import json
from datetime import datetime
import services
from services import (
    login_to_system,
    query_user_auth,
    query_auth_and_coupon,
    query_account_by_pin,
    query_account_by_phone,
    batch_query_phone_by_pin,
    query_ip_location,
    batch_query_ip_location,
    add_merchant_to_whitelist,
    get_active_queries,
    abort_query,
    is_query_aborted,
)
from config import LOGIN_CONFIG, CORS_CONFIG
from threading import Event
import threading

# 初始化Flask应用
app = Flask(__name__, static_folder="static", static_url_path="/static")
app.secret_key = "haili_web_secret_key_2024"  # 用于session管理

# 配置CORS以支持跨域请求
CORS(app, resources={r"/*": CORS_CONFIG})

app.config["CORS_HEADERS"] = "Content-Type"

# 全局session变量
GLOBAL_SESSION = None
LOGIN_STATUS = {"success": False, "message": "未初始化"}

# 全局变量存储活跃的查询任务
active_queries = {}

# 定义菜单项
MENU_ITEMS = [
    {"id": "user-auth-query", "label": "查询用户实名认证", "icon": "fas fa-id-card"},
    {
        "id": "auth-coupon-query",
        "label": "查询实名认证和优惠券",
        "icon": "fas fa-search",
    },
    {"id": "pin-to-account", "label": "PIN查询账号信息", "icon": "fas fa-user-search"},
    {"id": "phone-to-account", "label": "手机号查询账号", "icon": "fas fa-mobile-alt"},
    {"id": "batch-pin-query", "label": "批量PIN查询手机号", "icon": "fas fa-list"},
    {
        "id": "ip-location-query",
        "label": "IP归属地查询",
        "icon": "fas fa-map-marker-alt",
    },
    # {"id": "coupon-whitelist", "label": "消费券商家白名单", "icon": "fas fa-store"},
]


def auto_login():
    """应用启动时自动登录"""
    global GLOBAL_SESSION, LOGIN_STATUS

    # 如果已经登录成功，直接返回状态
    if GLOBAL_SESSION and LOGIN_STATUS.get("success"):
        print("已经登录，跳过自动登录")
        return LOGIN_STATUS

    try:
        print("开始自动登录...")
        # 检查配置是否为空
        if not LOGIN_CONFIG["username"] or not LOGIN_CONFIG["password"]:
            print("配置检查失败：用户名或密码为空")
            GLOBAL_SESSION = None
            LOGIN_STATUS = {
                "success": False,
                "message": "请检查配置的用户名和密码",
            }
            return LOGIN_STATUS

        print(f"尝试登录用户: {LOGIN_CONFIG['username']}")
        result = login_to_system(LOGIN_CONFIG["username"], LOGIN_CONFIG["password"])
        if result["success"]:
            print("自动登录成功！")
            GLOBAL_SESSION = result["session"]
            LOGIN_STATUS = {"success": True, "message": "登录成功"}
        else:
            print(f"自动登录失败: {result['message']}")
            GLOBAL_SESSION = None
            LOGIN_STATUS = {"success": False, "message": result["message"]}
        return LOGIN_STATUS
    except Exception as e:
        print(f"自动登录异常: {str(e)}")
        GLOBAL_SESSION = None
        LOGIN_STATUS = {"success": False, "message": f"自动登录失败: {str(e)}"}
        return LOGIN_STATUS


def get_recent_files(file_type="all", limit=10):
    """获取最近的查询结果文件"""
    try:
        output_dir = "downloads"
        if not os.path.exists(output_dir):
            return []

        # 根据文件类型获取文件
        if file_type == "pin":
            pattern = os.path.join(output_dir, "pin_*.txt")
        elif file_type == "ip":
            pattern = os.path.join(output_dir, "ip_location_*.csv")
        else:
            # 获取所有类型的文件
            pin_files = glob.glob(os.path.join(output_dir, "pin_*.txt"))
            ip_files = glob.glob(os.path.join(output_dir, "ip_location_*.csv"))
            files = pin_files + ip_files
            # 跳过glob.glob调用，直接使用files
            files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            files = files[:limit]

            # 构建文件信息
            file_info = []
            for file_path in files:
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))

                file_info.append(
                    {
                        "filename": filename,
                        "filepath": file_path,
                        "size": file_size,
                        "modified": mod_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "type": "PIN查询" if filename.startswith("pin_") else "IP查询",
                    }
                )
            return file_info

        files = glob.glob(pattern)

        # 按修改时间排序，最新的在前
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

        # 限制返回数量
        files = files[:limit]

        # 构建文件信息
        file_info = []
        for file_path in files:
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))

            file_info.append(
                {
                    "filename": filename,
                    "filepath": file_path,
                    "size": file_size,
                    "modified": mod_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "type": "PIN查询" if filename.startswith("pin_") else "IP查询",
                }
            )

        return file_info

    except Exception as e:
        print(f"获取文件列表失败: {str(e)}")
        return []


@app.route("/")
def index():
    """主页"""
    # 启动时自动登录
    login_status = auto_login()
    return render_template(
        "index.html", menu_items=MENU_ITEMS, login_status=login_status
    )


@app.route("/api/query_user_auth", methods=["POST"])
def api_query_user_auth():
    """查询用户实名认证API"""
    data = request.get_json()
    name = data.get("name")
    phone = data.get("phone")
    idcard = data.get("idcard")

    if not all([name, phone, idcard]):
        return jsonify(
            {
                "success": False,
                "message": "请填写完整的用户信息（姓名、手机号、身份证号）",
            }
        )

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return jsonify({"success": False, "message": "系统未登录，请检查配置"})

    try:
        result = query_user_auth(GLOBAL_SESSION, name, phone, idcard)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": f"查询出错: {str(e)}"})


@app.route("/api/query_auth_coupon", methods=["POST"])
def api_query_auth_coupon():
    """查询实名认证和优惠券情况API"""
    data = request.get_json()
    name = data.get("name")
    phone = data.get("phone")
    idcard = data.get("idcard")

    if not all([name, phone, idcard]):
        return jsonify(
            {
                "success": False,
                "message": "请填写完整的用户信息（姓名、手机号、身份证号）",
            }
        )

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return jsonify({"success": False, "message": "系统未登录，请检查配置"})

    try:
        result = query_auth_and_coupon(GLOBAL_SESSION, name, phone, idcard)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": f"查询出错: {str(e)}"})


@app.route("/api/query_pin_account", methods=["POST"])
def api_query_pin_account():
    """PIN查询账号信息API"""
    data = request.get_json()
    pin = data.get("pin")

    if not pin:
        return jsonify({"success": False, "message": "请输入用户PIN"})

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return jsonify({"success": False, "message": "系统未登录，请检查配置"})

    try:
        result = query_account_by_pin(GLOBAL_SESSION, pin)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": f"查询出错: {str(e)}"})


@app.route("/api/query_phone_account", methods=["POST"])
def api_query_phone_account():
    """手机号查询账号API"""
    data = request.get_json()
    phone = data.get("phone")

    if not phone:
        return jsonify({"success": False, "message": "请输入手机号"})

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return jsonify({"success": False, "message": "系统未登录，请检查配置"})

    try:
        result = query_account_by_phone(GLOBAL_SESSION, phone)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": f"查询出错: {str(e)}"})


@app.route("/api/batch_pin_upload", methods=["POST"])
def api_batch_pin_upload():
    """批量PIN文件上传API"""
    if "file" not in request.files:
        return jsonify({"success": False, "message": "没有上传文件"})

    file = request.files["file"]
    if file.filename == "":
        return jsonify({"success": False, "message": "没有选择文件"})

    if not file.filename.lower().endswith(".txt"):
        return jsonify({"success": False, "message": "请上传TXT文件"})

    try:
        file_content = file.read().decode("utf-8")
        pins = [line.strip() for line in file_content.split("\n") if line.strip()]

        return jsonify(
            {
                "success": True,
                "message": f"文件 {file.filename} 上传成功，包含 {len(pins)} 个PIN",
                "file_content": file_content,
            }
        )
    except Exception as e:
        return jsonify({"success": False, "message": f"文件上传失败: {str(e)}"})


@app.route("/api/batch_pin_query", methods=["POST"])
def api_batch_pin_query():
    """批量PIN查询API"""
    data = request.get_json()
    file_content = data.get("file_content")
    query_id = data.get("query_id")  # 前端传入的查询ID

    if not file_content:
        return jsonify({"success": False, "message": "没有文件内容"})

    # 如果存在旧的查询，先中断它
    if query_id in services.active_queries:
        services.abort_query(query_id)  # 中断旧查询

    try:
        result = batch_query_phone_by_pin(
            GLOBAL_SESSION, file_content, query_id=query_id
        )

        # 查询完成后清理已在services.py中处理

        return jsonify(result)

    except Exception as e:
        # 已在services.py中处理异常清理
        return jsonify({"success": False, "message": f"查询出错: {str(e)}"})


@app.route("/api/batch_pin_stats", methods=["POST"])
def api_batch_pin_stats():
    """获取批量PIN查询统计信息API"""
    data = request.get_json()
    file_content = data.get("file_content")

    if not file_content:
        return jsonify({"success": False, "message": "没有文件内容"})

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return jsonify({"success": False, "message": "系统未登录，请检查配置"})

    try:
        result = batch_query_phone_by_pin(
            GLOBAL_SESSION, file_content, return_stats_only=True
        )
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": f"获取统计信息失败: {str(e)}"})


@app.route("/api/ip_location_upload", methods=["POST"])
def api_ip_location_upload():
    """IP归属地文件上传API"""
    if "file" not in request.files:
        return jsonify({"success": False, "message": "没有上传文件"})

    file = request.files["file"]
    if file.filename == "":
        return jsonify({"success": False, "message": "没有选择文件"})

    if not file.filename.lower().endswith(".csv"):
        return jsonify({"success": False, "message": "请上传CSV文件"})

    try:
        file_content = file.read().decode("utf-8")
        lines = [line.strip() for line in file_content.split("\n") if line.strip()]

        return jsonify(
            {
                "success": True,
                "message": f"文件 {file.filename} 上传成功，包含 {len(lines)} 个IP",
                "file_content": file_content,
            }
        )
    except Exception as e:
        return jsonify({"success": False, "message": f"文件上传失败: {str(e)}"})


@app.route("/api/ip_location_stats", methods=["POST"])
def api_ip_location_stats():
    """获取IP归属地查询统计信息API"""
    data = request.get_json()
    file_content = data.get("file_content")

    if not file_content:
        return jsonify({"success": False, "message": "没有文件内容"})

    try:
        result = query_ip_location(file_content, return_stats_only=True)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": f"获取统计信息失败: {str(e)}"})


@app.route("/api/ip_location_query", methods=["POST"])
def api_ip_location_query():
    """IP归属地查询API"""
    try:
        data = request.get_json()
        file_content = data.get("file_content")
        query_id = data.get("query_id")

        if not file_content:
            return jsonify({"success": False, "message": "没有文件内容"})

        result = batch_query_ip_location(file_content, query_id=query_id)
        return jsonify(result)

    except Exception as e:
        return jsonify({"success": False, "message": f"查询出错: {str(e)}"})


@app.route("/api/get_recent_files")
def api_get_recent_files():
    """获取最近文件列表API"""
    file_type = request.args.get("type", "all")
    limit = int(request.args.get("limit", 10))

    try:
        files = get_recent_files(file_type, limit)
        return jsonify({"success": True, "files": files})
    except Exception as e:
        return jsonify({"success": False, "message": f"获取文件列表失败: {str(e)}"})


@app.route("/api/download_file/<filename>")
def api_download_file(filename):
    """文件下载API"""
    try:
        file_path = os.path.join("downloads", filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({"success": False, "message": "文件不存在"}), 404
    except Exception as e:
        return jsonify({"success": False, "message": f"下载失败: {str(e)}"}), 500


@app.route("/api/login_status")
def api_login_status():
    """获取登录状态API"""
    return jsonify(LOGIN_STATUS)


@app.route("/api/check_active_queries", methods=["GET"])
def api_check_active_queries():
    """检查活跃的查询"""
    try:
        active_queries = get_active_queries()
        return jsonify({"success": True, "activeQueries": active_queries})
    except Exception as e:
        return jsonify({"success": False, "message": f"检查活跃查询失败: {str(e)}"})


@app.route("/api/abort_query", methods=["POST"])
def api_abort_query():
    """中断查询"""
    try:
        data = request.get_json()
        query_id = data.get("query_id")

        if not query_id:
            return jsonify({"success": False, "message": "未提供查询ID"})

        if abort_query(query_id):
            return jsonify({"success": True, "message": "查询已中断"})
        else:
            return jsonify({"success": False, "message": "未找到对应的查询任务"})
    except Exception as e:
        return jsonify({"success": False, "message": f"中断查询失败: {str(e)}"})


if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=8050)
